from django.contrib import admin

from stocks.models import HistoricalData, Indicators, Stock


# Customize the Stock admin interface
class StockAdmin(admin.ModelAdmin):
    search_fields = ["symbol", "name"]
    list_display = (
        "symbol",
        "name",
    )


# Customize the HistoricalData admin interface
class HistoricalDataAdmin(admin.ModelAdmin):
    search_fields = ["stock__symbol", "stock__name", "date"]
    list_display = (
        "stock",
        "date",
        "open",
        "high",
        "low",
        "close",
        "volume",
    )


# Customize the Indicators admin interface
class IndicatorsAdmin(admin.ModelAdmin):
    search_fields = ["stock__symbol", "stock__name"]
    list_display = (
        "stock",
        "cci",
        "rv",
        "mfi",
        "performance_index",
        "ao",
    )


# Register your models with custom admin interfaces
admin.site.register(Stock, StockAdmin)
admin.site.register(HistoricalData, HistoricalDataAdmin)
admin.site.register(Indicators, IndicatorsAdmin)
