{% extends 'base.html' %}
{% load humanize %}
{% load custom_template_tags %}
{% block title %}Portfolio Targets{% endblock %}

{% block content %}
<div class="container my-4">
    <h1 class="text-center">Portfolio Target Tracker</h1>

    <!-- Add New Target Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-dark">
                <div class="card-body">
                    <h5 class="card-title">Add New Target</h5>
                    <form method="POST" action="{% url 'portfolio_add' %}">
                        {% csrf_token %}
                        <div class="row g-3">
                            {% for field in form %}
                            <div class="col-md-4">
                                <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                {{ field|add_class:"form-control bg-dark text-white" }}
                                {% if field.help_text %}
                                <small class="form-text text-muted">{{ field.help_text }}</small>
                                {% endif %}
                                {% if field.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ field.errors|join:", " }}
                                </div>
                                {% endif %}
                            </div>
                            {% endfor %}
                            <div class="col-12 mt-3">
                                <button type="submit" class="btn btn-primary">
                                    {% if target %}Update{% else %}Add{% endif %} Target
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages -->
    {% if messages %}
    <div class="row mb-4">
        <div class="col-12">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Targets Table -->
    <div class="row">

        <!-- Targets Table Block -->
        <div class="col-12">
            <div class="border p-3">
                <div class="table-responsive-sm">
                    <table class="table table-sm table-dark table-striped table-hover">
                        <thead>
                            <tr>
                                {% for column in columns %}
                                <th scope="col">
                                    <a
                                        href="{% if request.GET %}?{% for key, value in request.GET.items %}{% if key != 's' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}s={% if request.GET.s == column.key %}-{{ column.key }}{% elif request.GET.s == '-'|add:column.key %}{{ column.key }}{% else %}{{ column.key }}{% endif %}{% else %}?s={{ column.key }}{% endif %}">
                                        {{ column.label }}
                                        {% if request.GET.s == column.key %}
                                        <span>&#9650;</span>
                                        {% elif request.GET.s == '-'|add:column.key %}
                                        <span>&#9660;</span>
                                        {% endif %}
                                    </a>
                                </th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for target in targets %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>
                                    <a href="https://stockta.com/cgi-bin/analysis.pl?symb={{ target.stock.symbol }}&cobrand=&mode=stock"
                                        target="_blank">{{ target.stock.symbol }}</a>
                                </td>
                                <td>
                                    <a href="{% url 'historical_view' %}?symbol={{ target.stock.symbol }}">
                                        {{ target.stock.name }}
                                    </a>
                                </td>
                                <td>
                                    ${{ target.stock.price|default:"-"|floatformat:2 }}
                                    <span
                                        class="{% if target.stock.change_percentage > 0 %}text-success{% elif target.stock.change_percentage < 0 %}text-danger{% endif %}">
                                        ({{ target.stock.change_percentage|default:"-"|floatformat:2 }}%)
                                    </span>
                                </td>
                                <td>${{ target.target_price|floatformat:2 }}</td>
                                <td {% if target.target_reached %}class="text-success" {% elif target.potential_gain < 0
                                    %}class="text-danger" {% endif %}>
                                    {{ target.potential_gain|floatformat:1 }}%
                                </td>
                                <td>{{ target.stock.indicators.cci|default:"-"|floatformat:0 }}</td>
                                <td>{{ target.stock.indicators.rv|default:"-"|floatformat:0 }}</td>
                                <td>{{ target.stock.indicators.ao|default:"-"|floatformat:2 }}</td>
                                <td>{{ target.stock.indicators.mfi|default:"-"|floatformat:0 }}</td>
                                <td>
                                    {% if target.stock.price and target.stock.indicators.sma150 %}
                                    {{ target.stock.price|ma150_percentage:target.stock.indicators.sma150|floatformat:2 }}%
                                    {% else %}
                                    -
                                    {% endif %}
                                </td>
                                <td>{{ target.stock.indicators.ma_env_slope|default:"-"|floatformat:2 }}</td>
                                <td>{{ target.stock.indicators.stoch_k|default:"-"|floatformat:0 }}</td>
                                <td>{{ target.stock.indicators.stoch_d|default:"-"|floatformat:0 }}</td>
                                <td>
                                    <span class="rank-movement"
                                        title="Previous: {{ target.previous_rank|default:'0' }} → Current: {{ target.current_rank|default:'0' }}">
                                        {% if target.previous_rank and target.current_rank %}
                                        {% if target.current_rank > target.previous_rank %}
                                        <span class="text-success">
                                            ↑ +{{ target.current_rank|subtract:target.previous_rank }}
                                        </span>
                                        {% elif target.current_rank < target.previous_rank %}
                                        <span class="text-danger">
                                            ↓ -{{ target.previous_rank|subtract:target.current_rank }}
                                        </span>
                                        {% else %}
                                        <span class="text-muted">→</span>
                                        {% endif %}
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </span>
                                </td>
                                <td>
                                    {% if target.target_reached %}
                                    <span class="badge bg-success">Reached</span>
                                    {% else %}
                                    <span class="badge bg-primary">Active</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'portfolio_edit' target.pk %}"
                                        class="btn btn-sm btn-warning">Edit</a>
                                    <form method="POST" action="{% url 'portfolio_delete' target.pk %}"
                                        class="d-inline">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-sm btn-danger"
                                            onclick="return confirm('Are you sure you want to delete this target?')">
                                            Delete
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}