import os
import time
from concurrent.futures import Thread<PERSON>oolExecutor
from datetime import datetime, timedelta
from multiprocessing import freeze_support

import pandas as pd
from common.utils import (
    DBConnection,
    chunk_list,
    convert_numpy_bool,
    convert_to_float,
    execute_bulk_insert,
    get_stock_symbols,
)


def calculate_stochastic(
    df,
    k_period=10,
    smoothing_period=3,
    double_smoothing_period=3,
    d_period=10,
):
    """Calculate Stochastic Momentum Index (SMI) using EMA smoothing"""
    try:
        # Calculate highest high and lowest low
        highest_high = df["high"].rolling(window=k_period).max()
        lowest_low = df["low"].rolling(window=k_period).min()

        # Calculate median price and ranges
        median_price = (highest_high + lowest_low) / 2
        relative_range = df["close"] - median_price
        highest_lowest_range = highest_high - lowest_low

        # Double EMA smoothing of the ranges
        relative_range_smooth1 = relative_range.ewm(
            span=smoothing_period, adjust=False
        ).mean()
        relative_range_smooth2 = relative_range_smooth1.ewm(
            span=double_smoothing_period, adjust=False
        ).mean()

        range_smooth1 = highest_lowest_range.ewm(
            span=smoothing_period, adjust=False
        ).mean()
        range_smooth2 = range_smooth1.ewm(
            span=double_smoothing_period, adjust=False
        ).mean()

        # Calculate SMI
        df["smi"] = 200 * (relative_range_smooth2 / range_smooth2)

        # Calculate signal line using EMA
        df["smi_signal"] = df["smi"].ewm(span=d_period, adjust=False).mean()

        # Add crossover detection
        df["smi_crossover"] = (df["smi"] > df["smi_signal"]) & (
            df["smi"].shift(1) <= df["smi_signal"].shift(1)
        )

        return df
    except Exception as e:
        print(f"Error calculating SMI: {e}")
        return df


def fetch_and_calculate_stochastic(data_chunk):
    """Calculate Stochastic for the provided chunk of stock data"""
    try:
        stoch_data = []
        temp_dict = {}

        # Group data by symbol
        for record in data_chunk:
            symbol = record[0]
            data = record[1:]

            if symbol not in temp_dict:
                temp_dict[symbol] = []
            temp_dict[symbol].append(data)

        # Calculate Stochastic for each symbol
        for symbol, records in temp_dict.items():
            df = pd.DataFrame(records, columns=["date", "high", "low", "close"])
            df = calculate_stochastic(df)

            # Get latest values
            latest_data = df.iloc[-1] if not df.empty else None

            if latest_data is not None and pd.notna(latest_data["smi"]):
                stoch_data.append(
                    (
                        symbol,
                        convert_to_float(latest_data["smi"]),
                        convert_to_float(latest_data["smi_signal"]),
                        convert_numpy_bool(latest_data["smi_crossover"]),
                        "NOW()",
                        "NOW()",
                    )
                )

        return stoch_data
    except Exception as e:
        print(f"Error in fetch_and_calculate_stochastic: {e}")
        return []


def main(request):
    try:
        start_time = time.time()

        # Single database connection for the entire process
        with DBConnection() as connection:
            with connection.cursor() as cursor:
                # Get stock symbols
                stock_symbols = get_stock_symbols(cursor=cursor, only_symbols=True)
                # stock_symbols = ["AAPL", "NEXT"]
                print(f"Retrieved {len(stock_symbols)} stock symbols.")

                # Create generator for symbol chunks
                chunks = chunk_list(stock_symbols, 25)

                # Calculate lookback period
                lookback_days = datetime.now() - timedelta(days=365)
                lookback_date = lookback_days.strftime("%Y-%m-%d")

                # Process each chunk using ThreadPoolExecutor
                with ThreadPoolExecutor(max_workers=5) as executor:
                    futures = []

                    # Iterate over generator and submit tasks
                    for symbols_chunk in chunks:
                        # Fetch data for current chunk
                        symbol_placeholders = ",".join(["%s"] * len(symbols_chunk))
                        query = f"""
                            SELECT s.symbol, h.date, h.high, h.low, h.close
                            FROM stocks_stock s
                            JOIN stocks_historicaldata h ON s.symbol = h.stock_id
                            WHERE s.symbol IN ({symbol_placeholders}) AND h.date >= %s
                            ORDER BY s.symbol, h.date;
                        """.strip()
                        cursor.execute(query, symbols_chunk + [lookback_date])
                        chunk_data = cursor.fetchall()

                        # Submit Stochastic calculation to thread pool
                        futures.append(
                            executor.submit(fetch_and_calculate_stochastic, chunk_data)
                        )

                    # Collect results as they complete
                    stoch_data = []
                    for future in futures:
                        chunk_result = future.result()
                        stoch_data.extend(chunk_result)

                print(f"Calculated Stochastic for {len(stoch_data)} stocks")

                if stoch_data:
                    insert_query = """
                        INSERT INTO stocks_indicators
                        (stock_id, stoch_k, stoch_d, stoch_crossover, updated_at, created_at)
                        VALUES %s
                        ON CONFLICT (stock_id) DO UPDATE
                        SET
                            stoch_k = EXCLUDED.stoch_k,
                            stoch_d = EXCLUDED.stoch_d,
                            stoch_crossover = EXCLUDED.stoch_crossover,
                            updated_at = NOW();
                    """.strip()

                    execute_bulk_insert(connection, cursor, insert_query, stoch_data)
                    print("Bulk insert completed")

        end_time = time.time()
        execution_time = end_time - start_time
        print(f"Execution time: {execution_time} seconds")

        return f"Stochastic values calculated for {len(stoch_data)} stocks.", 200

    except Exception as e:
        print(f"Error in main function: {e}")
        raise


if __name__ == "__main__" and os.getenv("SERVER") == "development":
    freeze_support()
    main("")
