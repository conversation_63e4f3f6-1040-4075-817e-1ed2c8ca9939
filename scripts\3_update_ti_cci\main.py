import os
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON>Executor
from datetime import datetime, timed<PERSON><PERSON>
from multiprocessing import freeze_support

import pandas as pd
from common.utils import (
    DBConnection,
    calculate_cci,
    chunk_list,
    convert_to_float,
    execute_bulk_insert,
    get_stock_symbols,
)


def fetch_and_calculate_cci(data_chunk):
    """Calculate CCI for the provided chunk of stock data"""
    try:
        cci_data = []
        temp_dict = {}

        # Group data by symbol
        for record in data_chunk:
            symbol = record[0]
            data = record[1:]

            if symbol not in temp_dict:
                temp_dict[symbol] = []

            temp_dict[symbol].append(data)

        # Calculate CCI for each symbol
        for symbol, records in temp_dict.items():
            df = pd.DataFrame(records, columns=["date", "high", "low", "close"])
            df["cci"] = calculate_cci(df)
            latest_cci = (
                df["cci"].dropna().iloc[-1] if not df["cci"].dropna().empty else None
            )

            if pd.notna(latest_cci):
                cci_data.append(
                    (
                        symbol,
                        convert_to_float(latest_cci),
                        "NOW()",
                        "NOW()",
                    )
                )

        return cci_data
    except Exception as e:
        print(f"Error in fetch_and_calculate_cci: {e}")
        return []


def main(request):
    try:
        # Single database connection for the entire process
        with DBConnection() as connection:
            with connection.cursor() as cursor:
                # Get stock symbols
                stock_symbols = get_stock_symbols(cursor=cursor, only_symbols=True)
                # stock_symbols = ["AAPL", "^GSPC"]
                print(f"Retrieved {len(stock_symbols)} stock symbols.")

                # Create generator for symbol chunks
                chunks = chunk_list(stock_symbols, 25)

                format = "%Y-%m-%d"
                one_year_ago = (datetime.now() - timedelta(days=365)).strftime(format)

                # Process each chunk using ThreadPoolExecutor
                with ThreadPoolExecutor(max_workers=5) as executor:
                    futures = []

                    # Iterate over generator and submit tasks
                    for symbols_chunk in chunks:
                        # Fetch data for current chunk
                        symbol_placeholders = ",".join(["%s"] * len(symbols_chunk))
                        query = f"""
                            SELECT s.symbol, h.date, h.high, h.low, h.close
                            FROM stocks_stock s
                            JOIN stocks_historicaldata h ON s.symbol = h.stock_id
                            WHERE s.symbol IN ({symbol_placeholders}) AND h.date >= %s
                            ORDER BY s.symbol, h.date;
                        """.strip()
                        cursor.execute(query, symbols_chunk + [one_year_ago])
                        chunk_data = cursor.fetchall()

                        # Submit CCI calculation to thread pool
                        futures.append(
                            executor.submit(fetch_and_calculate_cci, chunk_data)
                        )

                    # Collect results as they complete
                    cci_data = []
                    for future in futures:
                        chunk_result = future.result()
                        cci_data.extend(chunk_result)

                print(f"Calculated CCI for {len(cci_data)} stocks")

                if cci_data:
                    insert_query = """
                        INSERT INTO stocks_indicators (stock_id, cci, updated_at, created_at)
                        VALUES %s
                        ON CONFLICT (stock_id) DO UPDATE
                        SET
                            cci = EXCLUDED.cci,
                            updated_at = NOW();
                    """.strip()

                    execute_bulk_insert(connection, cursor, insert_query, cci_data)
                    print("Bulk insert completed.")

        return f"CCI values inserted for {len(cci_data)} stocks.", 200

    except Exception as e:
        print(f"Error in main function: {e}")
        raise


if __name__ == "__main__":
    if os.getenv("SERVER") == "development":
        freeze_support()
        main("")
