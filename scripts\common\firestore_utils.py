import logging
import os
from datetime import datetime, timedelta

import pandas as pd
from common.utils import base64_to_dict
from google.cloud import firestore
from google.oauth2 import service_account

CONFIG = {
    "DB_NAME": "raja-db",
}


def update_sp500_ao_in_firestore(ao_value: float, date: datetime):
    """Update SP500 AO value in Firestore"""
    service_account_base64_str = os.getenv("BASE64_STR")
    if not service_account_base64_str:
        raise ValueError("Firestore credentials not found")

    service_account_info = base64_to_dict(service_account_base64_str)
    credentials = service_account.Credentials.from_service_account_info(
        service_account_info
    )
    db = firestore.Client(
        credentials=credentials,
        project=service_account_info["project_id"],
        database=CONFIG["DB_NAME"],
    )

    doc_ref = db.collection("sp500_ao").document(date.strftime("%Y-%m-%d"))
    doc_ref.set(
        {"ao_value": ao_value, "timestamp": firestore.SERVER_TIMESTAMP},
        merge=True,
    )
    logging.info(f"Updated SP500 AO value for {date.strftime('%Y-%m-%d')}")


def bulk_update_sp500_ao_history(df: pd.DataFrame, days: int = 30):
    """
    Bulk update SP500 AO values in Firestore for the last specified days

    Args:
        df: DataFrame with 'date' and 'ao' columns
        days: Number of days of history to update (default: 30)

    Returns:
        int: Number of records updated
    """
    # Get credentials and initialize Firestore
    service_account_base64_str = os.getenv("BASE64_STR")
    if not service_account_base64_str:
        raise ValueError("Firestore credentials not found")

    service_account_info = base64_to_dict(service_account_base64_str)
    credentials = service_account.Credentials.from_service_account_info(
        service_account_info
    )
    db = firestore.Client(
        credentials=credentials,
        project=service_account_info["project_id"],
        database="raja-db",
    )

    # Convert cutoff_date to date for comparison
    cutoff_date = (datetime.now() - timedelta(days=days)).date()

    # Ensure df['date'] is date type for comparison
    if isinstance(df["date"].iloc[0], datetime):
        df["date"] = df["date"].dt.date

    # Filter for SP500 and last N days
    mask = (df["symbol"] == "^GSPC") & (df["date"] >= cutoff_date)

    recent_data = df[mask].copy()

    if recent_data.empty:
        logging.warning("No recent SP500 data found")
        return 0

    # Sort by date to ensure chronological order
    recent_data = recent_data.sort_values("date")

    # Prepare batch
    batch = db.batch()
    count = 0

    # Create batches of 500 operations (Firestore limit)
    for _, row in recent_data.iterrows():
        doc_ref = db.collection("sp500_ao").document(row["date"].strftime("%Y-%m-%d"))
        batch.set(
            doc_ref,
            {"ao_value": float(row["ao"]), "timestamp": firestore.SERVER_TIMESTAMP},
            merge=True,
        )
        count += 1

        # Commit every 500 operations and start new batch
        if count % 500 == 0:
            batch.commit()
            batch = db.batch()

    # Commit any remaining operations
    if count % 500 != 0:
        batch.commit()

    logging.info(f"Updated {count} SP500 AO records in Firestore")
    return count
