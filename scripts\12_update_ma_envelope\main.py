import os
import time
from concurrent.futures import Thr<PERSON>PoolExecutor
from datetime import datetime, timedelta

import numpy as np
import pandas as pd
from common.utils import (
    DBConnection,
    chunk_list,
    convert_to_float,
    execute_bulk_insert,
    get_stock_symbols,
)


def calculate_ma_envelope_slope(df, period=50):
    """Calculate the slope of the MA envelope median line."""
    try:
        df["ma"] = df["close"].rolling(window=period).mean()
        if len(df["ma"].dropna()) > 5:
            recent_ma = df["ma"].dropna().tail(5)
            x = np.arange(len(recent_ma))
            y = recent_ma.values
            slope = np.polyfit(x, y, 1)[0]
            return (slope / recent_ma.mean()) * 100
        return None
    except Exception as e:
        print(f"Error calculating MA envelope slope: {e}")
        return None


def fetch_and_calculate_ma_envelope(data_chunk):
    """Calculate MA Envelope for the provided chunk of stock data"""
    try:
        ma_env_data = []
        temp_dict = {}

        # Group data by symbol
        for record in data_chunk:
            symbol = record[0]
            if symbol not in temp_dict:
                temp_dict[symbol] = []
            temp_dict[symbol].append((record[1], record[2]))

        # Calculate MA Envelope for each symbol
        for symbol, records in temp_dict.items():
            df = pd.DataFrame(records, columns=["date", "close"])
            slope = calculate_ma_envelope_slope(df)

            if pd.notna(slope):
                ma_env_data.append(
                    (
                        symbol,
                        convert_to_float(slope),
                        "NOW()",
                        "NOW()",
                    )
                )

        return ma_env_data
    except Exception as e:
        print(f"Error in fetch_and_calculate_ma_envelope: {e}")
        return []


def main(request):
    try:
        start_time = time.time()

        # Single database connection for the entire process
        with DBConnection() as connection:
            with connection.cursor() as cursor:
                # Get stock symbols
                stock_symbols = get_stock_symbols(cursor=cursor, only_symbols=True)
                # stock_symbols = ["AAPL"]
                print(f"Retrieved {len(stock_symbols)} stock symbols.")

                # Create generator for symbol chunks
                chunks = chunk_list(stock_symbols, 25)

                # Calculate lookback period
                lookback_days = datetime.now() - timedelta(days=100)
                one_year_ago = lookback_days.strftime("%Y-%m-%d")

                # Process each chunk using ThreadPoolExecutor
                with ThreadPoolExecutor(max_workers=5) as executor:
                    futures = []

                    # Iterate over generator and submit tasks
                    for symbols_chunk in chunks:
                        # Fetch data for current chunk
                        symbol_placeholders = ",".join(["%s"] * len(symbols_chunk))
                        query = f"""
                            SELECT s.symbol, h.date, h.close
                            FROM stocks_stock s
                            JOIN stocks_historicaldata h ON s.symbol = h.stock_id
                            WHERE s.symbol IN ({symbol_placeholders}) AND h.date >= %s
                            ORDER BY s.symbol, h.date;
                        """.strip()
                        cursor.execute(query, symbols_chunk + [one_year_ago])
                        chunk_data = cursor.fetchall()

                        # Submit MA Envelope calculation to thread pool
                        futures.append(
                            executor.submit(fetch_and_calculate_ma_envelope, chunk_data)
                        )

                    # Collect results as they complete
                    ma_env_data = []
                    for future in futures:
                        chunk_result = future.result()
                        ma_env_data.extend(chunk_result)

                print(f"Calculated MA Envelope for {len(ma_env_data)} stocks")

                if ma_env_data:
                    insert_query = """
                        INSERT INTO stocks_indicators (stock_id, ma_env_slope, updated_at, created_at)
                        VALUES %s
                        ON CONFLICT (stock_id) DO UPDATE
                        SET
                            ma_env_slope = EXCLUDED.ma_env_slope,
                            updated_at = NOW();
                    """.strip()

                    execute_bulk_insert(connection, cursor, insert_query, ma_env_data)
                    print("Bulk insert completed.")

        end_time = time.time()
        execution_time = end_time - start_time
        print(f"Execution time: {execution_time} seconds")

        return f"MA Envelope slopes calculated for {len(ma_env_data)} stocks.", 200

    except Exception as e:
        print(f"Error in main function: {e}")
        raise


if __name__ == "__main__" and os.getenv("SERVER") == "development":
    main("")
