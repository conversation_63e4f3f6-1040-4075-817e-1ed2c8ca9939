name: Deploy to GAE

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  deploy:
    name: Deploying to Google Cloud
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup python 3.9
        uses: actions/setup-python@v2
        with:
          python-version: "3.9"

      - name: Copy secrets to .env
        run: |
          touch .env
          echo "DJANGO_SECRET_KEY='${{ secrets.DJANGO_SECRET_KEY }}'" >> .env  
          echo "DB_NAME='${{ secrets.DB_NAME }}'" >> .env  
          echo "DB_USER='${{ secrets.DB_USER }}'" >> .env  
          echo "DB_PWD='${{ secrets.DB_PWD }}'" >> .env  
          echo "DB_HOST='${{ secrets.DB_HOST }}'" >> .env  
          echo "BASE64_STR='${{ secrets.BASE64_STR }}'" >> .env  
          echo "SERVER='production'" >> .env  

      - name: Install Requirements
        run: |
          pip install -r requirements.txt

      - name: Set up gcloud Cloud SDK environment
        uses: google-github-actions/setup-gcloud@v2.1.0    
        with:
          project_id: ${{ secrets.PROJECT_ID }}
          service_account_key: ${{ secrets.SERVICE_ACCOUNT }}
          export_default_credentials: true

      - name: Gather Static Files
        run: |
          python manage.py collectstatic --noinput

      - name: Deploy to App Engine
        id: deploy
        uses: google-github-actions/deploy-appengine@v0.2.0
        with:
          deliverables: app.yaml
          version: v1
          project_id: ${{ secrets.PROJECT_ID }}
          credentials: ${{ secrets.SERVICE_ACCOUNT }}