# Generated by Django 4.2.13 on 2024-06-15 14:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("stocks", "0005_alter_indicators_cci_overbought_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="SavedFilter",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("filter_url", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
