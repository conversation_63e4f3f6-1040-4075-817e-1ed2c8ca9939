{% extends 'base.html' %}
{% load humanize %}
{% block title %}Saved Filters{% endblock %}

{% block content %}
<div class="container my-4">
    <h1 class="text-center">Saved Filters</h1>

    <div class="row">
        <!-- Filter Block -->
        <div class="col-12 mb-4">
            <div class="border p-3">
                <form method="get" action="{% url 'filter_operations' %}">
                    <div class="row">
                        <div class="form-group col-lg-12 col-md-12 col-12">
                            <label for="q">Search Saved List</label>
                            <input type="text" class="form-control" name="q" id="q" value="{{ request.GET.q }}"
                                placeholder="Enter name...!" />
                        </div>
                        <div class="form-group col-12">
                            <button type="submit" class="btn btn-primary mt-2">Search</button>
                            <button type="button" id="restoreOrderBtn" class="btn btn-secondary mt-2">Restore Order</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="container my-4">
    <div class="row">
        <!-- Table Block -->
        <div class="col-12">
            <div class="border p-3">
                <div class="table-responsive-sm">
                    <table id="filtersTable" class="table table-sm table-dark table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Name</th>
                                <th>Last Updated at</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for filter in filters %}
                            <tr id="filter-{{ filter.id }}" data-id="{{ filter.id }}">
                                <th scope="row">{{ forloop.counter0 }}</th>
                                <td><a href="{{ filter.filter_url }}" class="text-white">{{ filter.name }}</a></td>
                                <td>{{ filter.updated_at|naturaltime }}</td>
                                <td>
                                    <!-- Form for deleting a filter -->
                                    <form method="post" action="{% url 'filter_operations' %}" class="d-inline"
                                        onsubmit="return confirmDelete('{{ filter.name }}');">
                                        {% csrf_token %}
                                        <input type="hidden" name="delete" value="{{ filter.id }}">
                                        <button type="submit" class="btn btn-danger btn-sm">Delete</button>
                                    </form>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
<script>
    function confirmDelete(filterName) {
        return confirm(`Are you sure you want to delete the filter "${filterName}"?`);
    }

    $(function () {
        var $filtersTableBody = $("#filtersTable tbody");

        // Load order from local storage and rearrange rows
        var order = localStorage.getItem('filterOrder');
        if (order) {
            order = order.split(',');
            $.each(order, function (index, filterId) {
                var $row = $filtersTableBody.find('#filter-' + filterId);
                $filtersTableBody.append($row);
            });
        }

        // Make the table rows sortable
        $filtersTableBody.sortable({
            update: function (event, ui) {
                var order = $(this).sortable('toArray', { attribute: 'data-id' });
                localStorage.setItem('filterOrder', order.join(','));
            }
        }).disableSelection();

        // Restore original order button
        $("#restoreOrderBtn").click(function() {
            localStorage.removeItem('filterOrder');
            location.reload();  // Reload the page to reset the table order
        });
    });
</script>
{% endblock %}