{% extends 'base.html' %}
{% load custom_template_tags %}
{% load humanize %}
{% block title %}Stock List{% endblock %}
{% block content %}
<div class="container my-4">
    <h1 class="text-center">Stock List with Indicators</h1>

    <div class="row">
        <!-- Filter Block -->
        <div class="col-12 mb-4">
            <div class="border p-3">
                <form method="get" action="">
                    <div class="row">
                        <div class="form-group col-lg-4 col-md-6 col-12">
                            <label for="q">Search Stocks</label>
                            <input type="text" class="form-control" name="q" id="q" value="{{ request.GET.q }}"
                                placeholder="Enter stock symbols, separated by commas" />
                        </div>
                        <div class="form-group col-lg-4 col-md-6 col-12">
                            <label for="sector">Sector</label>
                            <select class="form-control" name="sector" id="sector" onchange="this.form.submit()">
                                <option value="">Any</option>
                                <!-- Add sectors dynamically -->
                            </select>
                        </div>
                        <div class="form-group col-lg-4 col-md-6 col-12">
                            <label for="price">Price</label>
                            <select class="form-control" name="price" id="price" onchange="this.form.submit()">
                                <option value="">Any</option>
                                <option value="u1">Under $1</option>
                                <option value="u2">Under $2</option>
                                <option value="u3">Under $3</option>
                                <option value="u4">Under $4</option>
                                <option value="u5">Under $5</option>
                                <option value="u7">Under $7</option>
                                <option value="u10">Under $10</option>
                                <option value="u15">Under $15</option>
                                <option value="u20">Under $20</option>
                                <option value="u30">Under $30</option>
                                <option value="u40">Under $40</option>
                                <option value="u50">Under $50</option>
                                <option value="o1">Over $1</option>
                                <option value="o2">Over $2</option>
                                <option value="o3">Over $3</option>
                                <option value="o4">Over $4</option>
                                <option value="o5">Over $5</option>
                                <option value="o7">Over $7</option>
                                <option value="o10">Over $10</option>
                                <option value="o15">Over $15</option>
                                <option value="o20">Over $20</option>
                                <option value="o30">Over $30</option>
                                <option value="o40">Over $40</option>
                                <option value="o50">Over $50</option>
                                <option value="o60">Over $60</option>
                                <option value="o70">Over $70</option>
                                <option value="o80">Over $80</option>
                                <option value="o90">Over $90</option>
                                <option value="o100">Over $100</option>
                                <option value="1to5">$1 to $5</option>
                                <option value="1to10">$1 to $10</option>
                                <option value="1to20">$1 to $20</option>
                                <option value="5to10">$5 to $10</option>
                                <option value="5to20">$5 to $20</option>
                                <option value="5to50">$5 to $50</option>
                                <option value="10to20">$10 to $20</option>
                                <option value="10to50">$10 to $50</option>
                                <option value="20to50">$20 to $50</option>
                                <option value="50to100">$50 to $100</option>
                            </select>
                        </div>
                        <div class="form-group col-lg-4 col-md-6 col-12">
                            <label for="change_percentage">Change Percentage</label>
                            <select class="form-control" name="change_percentage" id="change_percentage"
                                onchange="this.form.submit()">
                                <option value="">Any</option>
                                <option value="u">Up</option>
                                <option value="u1">Up 1%</option>
                                <option value="u2">Up 2%</option>
                                <option value="u3">Up 3%</option>
                                <option value="u4">Up 4%</option>
                                <option value="u5">Up 5%</option>
                                <option value="u6">Up 6%</option>
                                <option value="u7">Up 7%</option>
                                <option value="u8">Up 8%</option>
                                <option value="u9">Up 9%</option>
                                <option value="u10">Up 10%</option>
                                <option value="u15">Up 15%</option>
                                <option value="u20">Up 20%</option>
                                <option value="d">Down</option>
                                <option value="d1">Down 1%</option>
                                <option value="d2">Down 2%</option>
                                <option value="d3">Down 3%</option>
                                <option value="d4">Down 4%</option>
                                <option value="d5">Down 5%</option>
                                <option value="d6">Down 6%</option>
                                <option value="d7">Down 7%</option>
                                <option value="d8">Down 8%</option>
                                <option value="d9">Down 9%</option>
                                <option value="d10">Down 10%</option>
                                <option value="d15">Down 15%</option>
                                <option value="d20">Down 20%</option>
                            </select>
                        </div>
                        <div class="form-group col-lg-4 col-md-6 col-12">
                            <label for="avg_volume">Average Volume</label>
                            <select class="form-control" name="avg_volume" id="avg_volume"
                                onchange="this.form.submit()">
                                <option value="">Any</option>
                                <option value="u50">Under 50K</option>
                                <option value="u100">Under 100K</option>
                                <option value="u500">Under 500K</option>
                                <option value="u750">Under 750K</option>
                                <option value="u1000">Under 1M</option>
                                <option value="o50">Over 50K</option>
                                <option value="o100">Over 100K</option>
                                <option value="o200">Over 200K</option>
                                <option value="o300">Over 300K</option>
                                <option value="o400">Over 400K</option>
                                <option value="o500">Over 500K</option>
                                <option value="o750">Over 750K</option>
                                <option value="o1000">Over 1M</option>
                                <option value="o2000">Over 2M</option>
                                <option value="100to500">100K to 500K</option>
                                <option value="100to1000">100K to 1M</option>
                                <option value="500to1000">500K to 1M</option>
                                <option value="500to10000">500K to 10M</option>
                            </select>
                        </div>
                        <div class="form-group col-lg-4 col-md-6 col-12">
                            <label for="market_cap">Market Cap</label>
                            <select class="form-control" name="market_cap" id="market_cap"
                                onchange="this.form.submit()">
                                <option value="">Any</option>
                                <option value="mega">Mega ($200bln and more)</option>
                                <option value="large">Large ($10bln to $200bln)</option>
                                <option value="mid">Mid ($2bln to $10bln)</option>
                                <option value="small">Small ($300mln to $2bln)</option>
                                <option value="micro">Micro ($50mln to $300mln)</option>
                                <option value="nano">Nano (under $50mln)</option>
                                <option value="largeover">+Large (over $10bln)</option>
                                <option value="midover">+Mid (over $2bln)</option>
                                <option value="smallover">+Small (over $300mln)</option>
                                <option value="microover">+Micro (over $50mln)</option>
                                <option value="largeunder">-Large (under $200bln)</option>
                                <option value="midunder">-Mid (under $10bln)</option>
                                <option value="smallunder">-Small (under $2bln)</option>
                                <option value="microunder">-Micro (under $300mln)</option>
                            </select>
                        </div>
                        <div class="form-group col-lg-4 col-md-6 col-12">
                            <label for="cci">CCI</label>
                            <select class="form-control" name="cci" id="cci" onchange="this.form.submit()">
                                <option value="">Any</option>
                                <option value="ob100">Overbought (CCI > 100)</option>
                                <option value="ob50">Overbought (CCI > 50)</option>
                                <option value="os-100">Oversold (CCI < -100)</option>
                                <option value="os-50">Oversold (CCI < -50)</option>
                                <option value="positive">Positive (CCI > 0)</option>
                                <option value="negative">Negative (CCI < 0)</option>
                                <option value="50to100">Between 50 and 100</option>
                                <option value="neg100to50">Between -100 and -50</option>
                                <option value="neg50to0">Between -50 and 0</option>
                                <option value="0to50">Between 0 and 50</option>
                                <option value="custom">Custom Range</option>
                            </select>
                            <div id="cci_custom_range" style="display:none; margin-top: 10px;">
                                <div class="row">
                                    <div class="col">
                                        <input type="text" name="cci_min" id="cci_min" placeholder="Min CCI"
                                            class="form-control" />
                                    </div>
                                    <div class="col">
                                        <input type="text" name="cci_max" id="cci_max" placeholder="Max CCI"
                                            class="form-control" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-lg-4 col-md-6 col-12">
                            <label for="rv">RV</label>
                            <select class="form-control" name="rv" id="rv" onchange="this.form.submit()">
                                <option value="">Any</option>
                                <option value="ob70">Overbought (RV > 70)</option>
                                <option value="ob60">Overbought (RV > 60)</option>
                                <option value="os30">Oversold (RV < 30)</option>
                                <option value="os40">Oversold (RV < 40)</option>
                                <option value="positive">Positive (RV > 0)</option>
                                <option value="negative">Negative (RV < 0)</option>
                                <option value="50to70">Between 50 and 70</option>
                                <option value="30to50">Between 30 and 50</option>
                                <option value="30to70">Between 30 and 70</option>
                                <option value="0to30">Between 0 and 30</option>
                                <option value="custom">Custom Range</option>
                            </select>
                            <div id="rv_custom_range" style="display:none; margin-top: 10px;">
                                <div class="row">
                                    <div class="col">
                                        <input type="text" name="rv_min" id="rv_min" placeholder="Min RV"
                                            class="form-control" />
                                    </div>
                                    <div class="col">
                                        <input type="text" name="rv_max" id="rv_max" placeholder="Max RV"
                                            class="form-control" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-lg-4 col-md-6 col-12">
                            <label for="pi">Performance Index</label>
                            <select class="form-control" name="pi" id="pi" onchange="this.form.submit()">
                                <option value="">Any</option>
                                <option value="high">High Performance</option>
                                <option value="medium">Medium Performance</option>
                                <option value="low">Low Performance</option>
                                <option value="custom">Custom Range</option>
                            </select>
                            <div id="pi_custom_range" style="display:none; margin-top: 10px;">
                                <div class="row">
                                    <div class="col">
                                        <input type="text" name="pi_min" id="pi_min" placeholder="Min PI"
                                            class="form-control" />
                                    </div>
                                    <div class="col">
                                        <input type="text" name="pi_max" id="pi_max" placeholder="Max PI"
                                            class="form-control" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-lg-4 col-md-6 col-12">
                            <label for="ao">AO</label>
                            <select class="form-control" name="ao" id="ao" onchange="this.form.submit()">
                                <option value="">Any</option>
                                <option value="positive">Positive (AO > 0)</option>
                                <option value="negative">Negative (AO < 0)</option>
                                <option value="above50">Above 50 (AO > 50)</option>
                                <option value="below50">Below 50 (AO < 50)</option>
                                <option value="high">High (AO > 100)</option>
                                <option value="low">Low (AO < -100)</option>
                                <option value="50to100">Between 50 and 100</option>
                                <option value="0to50">Between 0 and 50</option>
                                <option value="neg50to0">Between -50 and 0</option>
                                <option value="neg100to50">Between -100 and -50</option>
                                <option value="custom">Custom Range</option>
                            </select>
                            <div id="ao_custom_range" style="display:none; margin-top: 10px;">
                                <div class="row">
                                    <div class="col">
                                        <input type="text" name="ao_min" id="ao_min" placeholder="Min AO"
                                            class="form-control" />
                                    </div>
                                    <div class="col">
                                        <input type="text" name="ao_max" id="ao_max" placeholder="Max AO"
                                            class="form-control" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-lg-4 col-md-6 col-12">
                            <label for="mfi">MFI</label>
                            <select class="form-control" name="mfi" id="mfi" onchange="this.form.submit()">
                                <option value="">Any</option>
                                <option value="ob80">Overbought (MFI > 80)</option>
                                <option value="os20">Oversold (MFI < 20)</option>
                                <option value="positive">Positive (MFI > 50)</option>
                                <option value="negative">Negative (MFI < 50)</option>
                                <option value="60to80">Between 60 and 80</option>
                                <option value="40to60">Between 40 and 60</option>
                                <option value="20to40">Between 20 and 40</option>
                                <option value="0to20">Between 0 and 20</option>
                                <option value="custom">Custom Range</option>
                            </select>
                            <div id="mfi_custom_range" style="display:none; margin-top: 10px;">
                                <div class="row">
                                    <div class="col">
                                        <input type="text" name="mfi_min" id="mfi_min" placeholder="Min MFI"
                                            class="form-control" />
                                    </div>
                                    <div class="col">
                                        <input type="text" name="mfi_max" id="mfi_max" placeholder="Max MFI"
                                            class="form-control" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Add this inside your filter form, with the other filter groups -->
                        <div class="form-group col-lg-4 col-md-6 col-12">
                            <label for="ma_env_slope">MA Envelope Slope</label>
                            <select class="form-control" name="ma_env_slope" id="ma_env_slope"
                                onchange="this.form.submit()">
                                <option value="">Any</option>
                                <option value="positive">Positive (> 0)</option>
                                <option value="negative">Negative (< 0)</option>
                                <option value="neg065to1">Between -0.65 and 1</option>
                                <option value="1to2">Between 1 and 2</option>
                                <option value="2to5">Between 2 and 5</option>
                                <option value="above5">Greater than 5</option>
                                <option value="custom">Custom Range</option>
                            </select>
                            <div id="ma_env_slope_custom_range" style="display:none; margin-top: 10px;">
                                <div class="row">
                                    <div class="col">
                                        <input type="text" name="ma_env_slope_min" id="ma_env_slope_min"
                                            placeholder="Min Slope" class="form-control" />
                                    </div>
                                    <div class="col">
                                        <input type="text" name="ma_env_slope_max" id="ma_env_slope_max"
                                            placeholder="Max Slope" class="form-control" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-lg-4 col-md-6 col-12">
                            <label for="ma150">150-day MA</label>
                            <select class="form-control" name="ma150" id="ma150" onchange="this.form.submit()">
                                <option value="">Any</option>
                                <option value="above">Price Above MA150</option>
                                <option value="below">Price Below MA150</option>
                            </select>
                        </div>
                        <!-- Add this alongside your other filter group divs -->
                        <div class="form-group col-lg-4 col-md-6 col-12">
                            <label for="stochastic">Stochastic Momentum</label>
                            <select class="form-control" name="stochastic" id="stochastic"
                                onchange="this.form.submit()">
                                <option value="">Any</option>
                                <option value="crossover">Bullish Crossover (Black crosses above Red)</option>
                                <option value="os20">Oversold (Black and Red below 20)</option>
                                <option value="ob80">Overbought (Black and Red above 80)</option>
                                <option value="k_above_d">Black Above Red Line</option>
                                <option value="k_below_d">Black Below Red Line</option>
                                <option value="custom">Custom Range</option>
                            </select>
                            <div id="stochastic_custom_range" style="display:none; margin-top: 10px;">
                                <div class="row">
                                    <div class="col">
                                        <input type="text" name="stochastic_min" id="stochastic_min"
                                            placeholder="Min Value" class="form-control" />
                                    </div>
                                    <div class="col">
                                        <input type="text" name="stochastic_max" id="stochastic_max"
                                            placeholder="Max Value" class="form-control" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-lg-4 col-md-6 col-12">
                            <label for="signal">Signal Stockscore</label>
                            <select class="form-control" name="signal" id="signal" onchange="this.form.submit()">
                                <option value="">Any</option>
                                <option value="0-20">0-20</option>
                                <option value="21-40">21-40</option>
                                <option value="41-60">41-60</option>
                                <option value="61-80">61-80</option>
                                <option value="81-100">81-100</option>
                                <option value="custom">Custom Range</option>
                            </select>
                            <div id="signal_custom_range" style="display:none; margin-top: 10px;">
                                <div class="row">
                                    <div class="col">
                                        <input type="text" name="signal_min" id="signal_min"
                                            placeholder="Min Signal Stockscore" class="form-control" />
                                    </div>
                                    <div class="col">
                                        <input type="text" name="signal_max" id="signal_max"
                                            placeholder="Max Signal Stockscore" class="form-control" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-lg-4 col-md-6 col-12">
                            <label for="sentiment">Sentiment Stockscore</label>
                            <select class="form-control" name="sentiment" id="sentiment" onchange="this.form.submit()">
                                <option value="">Any</option>
                                <option value="0-20">0-20</option>
                                <option value="21-40">21-40</option>
                                <option value="41-60">41-60</option>
                                <option value="61-80">61-80</option>
                                <option value="81-100">81-100</option>
                                <option value="custom">Custom Range</option>
                            </select>
                            <div id="sentiment_custom_range" style="display:none; margin-top: 10px;">
                                <div class="row">
                                    <div class="col">
                                        <input type="text" name="sentiment_min" id="sentiment_min"
                                            placeholder="Min Sentiment Stockscore" class="form-control" />
                                    </div>
                                    <div class="col">
                                        <input type="text" name="sentiment_max" id="sentiment_max"
                                            placeholder="Max Sentiment Stockscore" class="form-control" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-lg-4 col-md-6 col-12">
                            <label for="saved">Saved Filters</label>
                            <select class="form-control" name="saved" id="saved" onchange="this.form.submit()">
                                <option value="">Any</option>
                            </select>
                        </div>
                        <div class="form-group col-12">
                            <button type="submit" class="btn btn-primary mt-2">
                                Scan Stocks
                            </button>
                            <a href="/stocks/" class="btn btn-secondary mt-2">Reset Filters</a>
                            <button type="button" class="btn btn-warning mt-2" data-bs-toggle="modal"
                                data-bs-target="#saveFilterModal">
                                Save Filters
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="container my-4">
    <div class="row">
        <!-- Table Block -->
        <div class="col-12">
            <div class="border p-3">
                <div class="table-responsive-sm">
                    <table class="table table-sm table-dark table-striped table-hover">
                        <thead>
                            <tr>
                                {% for column in columns %}
                                <th scope="col">
                                    <a
                                        href="{% if request.GET %}?{% for key, value in request.GET.items %}{% if key != 's' %}{{ key }}={{ value|urlencode }}&{% endif %}{% endfor %}s={% if request.GET.s == column.key %}-{{ column.key }}{% elif request.GET.s == '-'|add:column.key %}{{ column.key }}{% else %}{{ column.key }}{% endif %}{% else %}?s={{ column.key }}{% endif %}">
                                        {{ column.label }}
                                        {% if request.GET.s == column.key %}
                                        <span>&#9650;</span>
                                        {% elif request.GET.s == '-'|add:column.key %}
                                        <span>&#9660;</span>
                                        {% endif %}
                                    </a>
                                </th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for stock in page_obj %}
                            <tr>
                                <!-- <th scope="row">
                                    {{ forloop.counter0|add:page_obj.start_index }}
                                </th> -->
                                <td>
                                    <a href="https://stockta.com/cgi-bin/analysis.pl?symb={{ stock.symbol }}&cobrand=&mode=stock"
                                        target="_blank">{{ stock.symbol }}</a>
                                </td>
                                <td>
                                    <a href="{% url 'historical_view' %}?symbol={{ stock.symbol }}">{{ stock.name }}</a>
                                </td>
                                <td>{{ stock.sector|default:"-" }}</td>
                                <td>${{ stock.price|default:"-"|floatformat:2 }}</td>
                                <td>{{ stock.change_percentage|default:"-"|floatformat:2 }}%</td>
                                <td>{{ stock.market_cap|default:"-"|intword }}</td>
                                <td>{{ stock.indicators.cci|default:"-"|floatformat:0 }}</td>
                                <td>{{ stock.indicators.rv|default:"-"|floatformat:0 }}</td>
                                <td>{{ stock.indicators.performance_index|default:"-"|floatformat:2 }}</td>
                                <td>{{ stock.indicators.ao|default:"-"|floatformat:2 }}</td>
                                <td>{{ stock.indicators.sentiment|default:"-"|floatformat:0 }}</td>
                                <td>{{ stock.indicators.signal|default:"-"|floatformat:0 }}</td>
                                <td>{{ stock.indicators.mfi|default:"-"|floatformat:0 }}</td>
                                <td>{{ stock.indicators.ma_env_slope|default:"-"|floatformat:2 }}</td>
                                <td>
                                    {% if stock.indicators.sma150 and stock.price %}
                                    {{ stock.price|ma150_percentage:stock.indicators.sma150|floatformat:2 }}%
                                    {% else %}
                                    -
                                    {% endif %}
                                </td>
                                <td>{{ stock.indicators.stoch_k|default:"-"|floatformat:0 }}</td>
                                <td>{{ stock.indicators.stoch_d|default:"-"|floatformat:0 }}</td>
                                <td>{{ stock.avg_volume|default:"-"|intcomma }}</td>
                                <td>
                                    {% if request.GET.q and stock.symbol in request.GET.q %}
                                    <button class="btn btn-sm btn-danger"
                                        onclick="showModal('{{ stock.symbol }}', 'remove')">-</button>
                                    {% else %}
                                    <button class="btn btn-sm btn-success"
                                        onclick="showModal('{{ stock.symbol }}', 'add')">+</button>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'portfolio_add' %}?symbol={{ stock.symbol }}"
                                        class="btn btn-sm btn-outline-success">
                                        + Portfolio
                                    </a>
                                </td>
                                <td>
                                    <form method="post" action="{% url 'delete_stock' symbol=stock.symbol %}"
                                        style="display: inline;"
                                        onsubmit="return confirm('Are you sure you want to delete {{ stock.symbol }}?');">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-sm btn-danger" title="Delete Stock">
                                            <i class="fas fa-trash"></i>
                                            X
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="row mt-4">
        <div class="col-12">
            <div class="border p-3 text-center">
                <p>Total Stocks: {{ page_obj.paginator.count }}</p>
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?{% update_query_params request page=1 %}">First</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link"
                            href="?{% update_query_params request page=page_obj.previous_page_number %}">Previous</a>
                    </li>
                    {% endif %}
                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > page_obj.number|add:-3 and num < page_obj.number|add:3 %} <li class="page-item">
                        <a class="page-link" href="?{% update_query_params request page=num %}">{{ num }}</a>
                        </li>
                        {% endif %} {% endfor %} {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link"
                                href="?{% update_query_params request page=page_obj.next_page_number %}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link"
                                href="?{% update_query_params request page=page_obj.paginator.num_pages %}">Last</a>
                        </li>
                        {% endif %}
                </ul>
            </div>
        </div>
    </div>
</div>
<!-- Add this modal code at the end of your template just before the closing body tag -->
<div class="modal fade" id="saveFilterModal" tabindex="-1" aria-labelledby="saveFilterModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content bg-dark text-white">
            <div class="modal-header">
                <h5 class="modal-title" id="saveFilterModalLabel">Save Filter</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'filter_operations' %}">
                    {% csrf_token %}
                    <input type="hidden" name="create" value="1" />
                    <input type="hidden" name="url" value="{{ request.get_full_path }}" />
                    <div class="mb-3">
                        <label for="filter-name" class="form-label">Filter Name</label>
                        <input type="text" class="form-control" id="filter-name" name="name" required />
                    </div>
                    <button type="submit" class="btn btn-primary">Save</button>
                </form>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="stockModal" tabindex="-1" aria-labelledby="stockModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header">
                <h5 class="modal-title" id="stockModalLabel">Modify Stock in List</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="stockForm">
                    <input type="hidden" name="stock_symbol" id="stockSymbol" />
                    <input type="hidden" name="action" id="actionType" />
                    <div class="mb-3">
                        <label for="listSelect" class="form-label">Select List</label>
                        <select class="form-select" id="listSelect" name="list_id">
                            <!-- Populate this select with your saved lists -->
                        </select>
                    </div>
                    <button type="button" class="btn btn-primary" onclick="submitStockForm()">Submit</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        // Fetch and populate saved filters with ordering
        fetch("/filters?format=json")
            .then((response) => response.json())
            .then((data) => {
                const savedDropdown = document.getElementById("saved");
                const savedDropdownModal = document.getElementById("listSelect");

                // Retrieve the saved order from local storage
                let order = localStorage.getItem('filterOrder');
                if (order) {
                    order = order.split(',');
                    const orderedData = [];

                    // Arrange data according to the saved order
                    order.forEach((filterId) => {
                        const filter = data.find(item => item.id === filterId);
                        if (filter) {
                            orderedData.push(filter);
                        }
                    });

                    // Add any remaining filters that were not in the saved order
                    data.forEach((filter) => {
                        if (!orderedData.some(orderedItem => orderedItem.id === filter.id)) {
                            orderedData.push(filter);
                        }
                    });

                    // Use orderedData for populating the dropdown
                    orderedData.forEach((filter) => {
                        const option1 = document.createElement("option");
                        option1.value = filter.name;
                        option1.textContent = filter.name;
                        savedDropdown.appendChild(option1);

                        const option2 = document.createElement("option");
                        option2.value = filter.name;
                        option2.textContent = filter.name;
                        savedDropdownModal.appendChild(option2);
                    });

                } else {
                    // If no order is saved, populate the dropdown with original data
                    data.forEach((filter) => {
                        const option1 = document.createElement("option");
                        option1.value = filter.name;
                        option1.textContent = filter.name;
                        savedDropdown.appendChild(option1);

                        const option2 = document.createElement("option");
                        option2.value = filter.name;
                        option2.textContent = filter.name;
                        savedDropdownModal.appendChild(option2);

                    });
                }
            })
            .catch((error) => console.error("Error fetching saved filters:", error));



        const sectorDropdown = document.getElementById("sector");
        const urlParams = new URLSearchParams(window.location.search);

        // Fetch and populate sectors
        fetch("/all-sectors/")
            .then((response) => response.json())
            .then((data) => {
                data.sectors.forEach((sector) => {
                    const option = document.createElement("option");
                    option.value = sector;
                    option.textContent = sector;
                    if (urlParams.get("sector") === sector) {
                        option.selected = true;
                    }
                    sectorDropdown.appendChild(option);
                });
            })
            .catch((error) => console.error("Error fetching sectors:", error));

        // Set selected values for other filters
        const filters = [
            "price",
            "change_percentage",
            "avg_volume",
            "market_cap",
            "cci",
            "cci_min",
            "cci_max",
            "rv",
            "rv_min",
            "rv_max",
            "mfi",
            "mfi_min",
            "mfi_max",
            "pi",
            "pi_min",
            "pi_max",
            "ao",
            "ao_min",
            "ao_max",
            "signal",
            "signal_min",
            "signal_max",
            "sentiment",
            "sentiment_min",
            "sentiment_max",
            "ma_env_slope",
            "ma_env_slope_min",
            "ma_env_slope_max",
            "ma150",
            "stochastic",
            "stochastic_min",
            "stochastic_max",
        ];
        filters.forEach((filter) => {
            const filterElement = document.getElementById(filter);
            if (filterElement) {
                const value = urlParams.get(filter);
                if (value) {
                    filterElement.value = value;
                }
            }
        });
    });
</script>
<script>
    document.addEventListener("DOMContentLoaded", function () {
        // Combined function to fetch stock data and handle popup functionality
        function fetchStockData() {
            let url = window.location.href;
            url += url.includes("?") ? "&format=json" : "?format=json";

            fetch(url)
                .then((response) => response.json())
                .then((data) => {
                    if (!data.stocks || data.stocks.length === 0) {
                        console.log("No data received from the API.");
                        return; // Skip updating the table if no data is returned
                    }

                    const tbody = document.querySelector("tbody");
                    tbody.innerHTML = ""; // Clear existing rows

                    data.stocks.forEach((stock) => {
                        const row = document.createElement("tr");

                        let actionButton;
                        const queryParams = new URLSearchParams(window.location.search);
                        const qParam = queryParams.get("q");

                        if (qParam && qParam.split(",").includes(stock.symbol)) {
                            actionButton = `<button class="btn btn-sm btn-danger" onclick="showModal('${stock.symbol}', 'remove')">-</button>`;
                        } else {
                            actionButton = `<button class="btn btn-sm btn-success" onclick="showModal('${stock.symbol}', 'add')">+</button>`;
                        }
                        // <th scope="row"></th>
                        row.innerHTML = `
                        <td><a href="https://stockta.com/cgi-bin/analysis.pl?symb=${stock.symbol}&cobrand=&mode=stock"
                        target="_blank">${stock.symbol}</a></td>
                        <td><a href="/historical/?symbol=${stock.symbol}">${stock.name}</td>
                        <td>${stock.sector || "-"}</td>
                        <td>${stock.price ? `$${parseFloat(stock.price).toFixed(2)}` : "-"}</td>
                        <td>${stock.change_percentage ? `${parseFloat(stock.change_percentage).toFixed(2)}%` : "-"}</td>
                        <td>${stock.market_cap ? stock.market_cap.toLocaleString() : "-"}</td>
                        <td>${stock.cci ? Math.round(stock.cci) : "-"}</td>
                        <td>${stock.rv ? Math.round(stock.rv) : "-"}</td>
                        <td>${stock.performance_index ? parseFloat(stock.performance_index).toFixed(2) : "-"}</td>
                        <td>${stock.ao ? parseFloat(stock.ao).toFixed(2) : "-"}</td>
                        <td>${stock.sentiment ? Math.round(stock.sentiment) : "-"}</td>
                        <td>${stock.signal ? Math.round(stock.signal) : "-"}</td>
                        <td>${stock.mfi ? Math.round(stock.mfi) : "-"}</td>
                        <td>${stock.ma_env_slope ? parseFloat(stock.ma_env_slope).toFixed(2) : "-"}</td>
                        <td>${(stock.price && stock.ma150) ? 
                        ((stock.price - stock.ma150) / stock.ma150 * 100).toFixed(2) + '%' : 
                        "-"}</td>
                        <td>${stock.stoch_k ? Math.round(stock.stoch_k) : "-"}</td>
                        <td>${stock.stoch_d ? Math.round(stock.stoch_d) : "-"}</td>
                        <td>${stock.avg_volume ? stock.avg_volume.toLocaleString() : "-"}</td>
                        <td>${actionButton}</td>
                        <td>
                            <a href="/portfolio/add/?symbol=${stock.symbol}" 
                            class="btn btn-sm btn-outline-success">
                            + Portfolio
                            </a>
                        </td>
                        <td>
                            <form method="post" action="/delete-stock/${stock.symbol}/" 
                                style="display: inline;"
                                onsubmit="return confirm('Are you sure you want to delete ${stock.symbol}?');">
                                <input type="hidden" name="csrfmiddlewaretoken" value="${document.querySelector('[name=csrfmiddlewaretoken]').value}">
                                <button type="submit" class="btn btn-sm btn-danger" title="Delete Stock">
                                    X
                                </button>
                            </form>
                        </td>`;
                        tbody.appendChild(row);
                    });
                })
                .catch((error) => console.error("Error fetching stock data:", error));
        }

        // Fetch stock data and setup table on page load
        fetchStockData();
        setInterval(fetchStockData, 60000); // Update stock data every 1 minute
    });
</script>
<script>
    document.addEventListener("DOMContentLoaded", function () {
        const filters = ["cci", "rv", "mfi", "pi", "ao", "signal", "sentiment", "ma_env_slope", "stochastic"];

        filters.forEach((filter) => {
            const filterElement = document.getElementById(filter);
            if (filterElement) {
                filterElement.addEventListener('change', function () {
                    toggleCustomRange(filter);
                });
                const customRangeDiv = document.getElementById(`${filter}_custom_range`);
                if (filterElement.value === 'custom') {
                    customRangeDiv.style.display = 'block';
                } else {
                    customRangeDiv.style.display = 'none';
                }
            }
        });

        // Initialize filters with URL parameters if present
        const urlParams = new URLSearchParams(window.location.search);
        filters.forEach((filter) => {
            const filterElement = document.getElementById(filter);
            if (filterElement) {
                const value = urlParams.get(filter);
                if (value) {
                    filterElement.value = value;
                    toggleCustomRange(filter);
                }
            }
        });
    });

    function toggleCustomRange(filter) {
        const customRangeDiv = document.getElementById(`${filter}_custom_range`);
        const filterElement = document.getElementById(filter);
        if (filterElement.value === 'custom') {
            customRangeDiv.style.display = 'block';
        } else {
            customRangeDiv.style.display = 'none';
        }
    }
</script>
<script>
    function showModal(stockSymbol, action) {
        document.getElementById('stockSymbol').value = stockSymbol;
        document.getElementById('actionType').value = action;
        new bootstrap.Modal(document.getElementById('stockModal')).show();
    }

    function submitStockForm() {
        const stockSymbol = document.getElementById('stockSymbol').value;
        const listId = document.getElementById('listSelect').value;
        const actionType = document.getElementById('actionType').value;

        fetch(`/modify-stock-in-list/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify({ stock_symbol: stockSymbol, list_id: listId, action: actionType })
        }).then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = data.filter_url; // Redirect to the updated filter URL
                } else {
                    alert("Error modifying stock in list");
                }
            });
    }
</script>
{% endblock %}