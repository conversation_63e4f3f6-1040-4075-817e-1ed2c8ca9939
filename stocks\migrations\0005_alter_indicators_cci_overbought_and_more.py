# Generated by Django 4.2.13 on 2024-06-13 10:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("stocks", "0004_stock_market_cap"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="indicators",
            name="cci_overbought",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="indicators",
            name="cci_oversold",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="indicators",
            name="mfi_overbought",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="indicators",
            name="mfi_oversold",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="indicators",
            name="rv_overbought",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="indicators",
            name="rv_oversold",
            field=models.FloatField(blank=True, null=True),
        ),
    ]
