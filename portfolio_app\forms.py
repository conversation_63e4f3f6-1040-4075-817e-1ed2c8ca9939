# portfolio/forms.py
from django import forms

from .models import PortfolioTarget


class PortfolioTargetForm(forms.ModelForm):
    symbol = forms.CharField(max_length=10, help_text="Enter stock symbol")

    class Meta:
        model = PortfolioTarget
        fields = [
            "target_price",
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.pk:
            self.initial["symbol"] = self.instance.stock.symbol
            self.fields["symbol"].disabled = True
