import os
from datetime import datetime, timedelta

import requests
from common.utils import base64_to_dict, get_random_user_agent
from google.cloud import firestore
from google.oauth2 import service_account

# Your service account
# key as a dictionary
# again
# deploying
service_account_base64_str = os.getenv("BASE64_STR") or None
service_account_info = base64_to_dict(service_account_base64_str)

# Initialize Firestore DB using the dictionary
credentials = service_account.Credentials.from_service_account_info(
    service_account_info
)
db = firestore.Client(
    credentials=credentials,
    project=service_account_info["project_id"],
    database="raja-db",
)


def fetch_fear_greed_index():
    api_url = "https://production.dataviz.cnn.io/index/fearandgreed/graphdata"
    headers = {"User-Agent": get_random_user_agent()}
    response = requests.get(api_url, headers=headers)
    data = response.json()
    return data.get("fear_and_greed", {}).get("score")


def fetch_cboe_ratios(date):
    date_str = date.strftime("%Y-%m-%d")
    api_url = f"https://cdn.cboe.com/data/us/options/market_statistics/daily/{date_str}_daily_options"
    headers = {"User-Agent": get_random_user_agent()}
    response = requests.get(api_url, headers=headers)

    if response.status_code == 200:
        data = response.json()
        total_ratio = None
        equity_ratio = None

        for ratio in data.get("ratios", []):
            if ratio.get("name") == "TOTAL PUT/CALL RATIO":
                total_ratio = ratio.get("value")
            elif ratio.get("name") == "EQUITY PUT/CALL RATIO":
                equity_ratio = ratio.get("value")

        return total_ratio, equity_ratio

    return None, None


def update_firestore(fear_greed_score, total_ratio, equity_ratio):
    doc_ref = db.collection("indices").document("fear_greed_and_vix")
    prev_value = doc_ref.get().to_dict()

    if prev_value:
        prev_score = prev_value.get("fear_greed_score")
    else:
        prev_score = None

    doc_ref.set(
        {
            "fear_greed_score": fear_greed_score,
            "total_put_call_ratio": total_ratio,
            "equity_put_call_ratio": equity_ratio,
            "timestamp": datetime.utcnow(),
            "was_above_75": prev_score is not None
            and prev_score > 75
            and fear_greed_score < 75,
        }
    )


def main(request):
    fear_greed_score = fetch_fear_greed_index()

    if fear_greed_score is None:
        return "Error: Failed to fetch Fear Greed Index", 500

    current_date = datetime.now().date()
    total_ratio = None
    equity_ratio = None

    while total_ratio is None or equity_ratio is None:
        total_ratio, equity_ratio = fetch_cboe_ratios(current_date)
        if total_ratio is None or equity_ratio is None:
            current_date -= timedelta(days=1)
            if current_date < datetime.now().date() - timedelta(days=30):
                return (
                    "Error: Failed to fetch CBOE ratios for the last 30 days",
                    500,
                )

    update_firestore(fear_greed_score, total_ratio, equity_ratio)
    return "Success: Indices updated", 200


# Main execution block to
# ensure this script runs standalone
if os.getenv("SERVER") == "development":
    if __name__ == "__main__":
        main("")
