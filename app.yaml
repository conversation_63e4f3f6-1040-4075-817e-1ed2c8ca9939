# [START django_app]
service: default
runtime: python39
handlers:
  # This configures Google App Engine to serve the files in the app's
  # static directory.
  - url: /static
    static_dir: static/
  # This handler routes all requests not caught above to the main app.
  # It is required when static routes are defined, but can be omitted
  # (along with the entire handlers section) when there are no static
  # files defined.
  - url: /.*
    script: auto

entrypoint: gunicorn -b :$PORT yahoo_stock_screener.wsgi --timeout 600