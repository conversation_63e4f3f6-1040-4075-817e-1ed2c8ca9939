import os
from concurrent.futures import Thr<PERSON><PERSON>ool<PERSON>xecutor
from datetime import datetime, timedelta
from multiprocessing import freeze_support

import pandas as pd
from common.utils import (
    DBConnection,
    calculate_performance_index,
    chunk_list,
    convert_to_float,
    execute_bulk_insert,
    get_stock_symbols,
    load_sp500_data,
)

# Load benchmark data once at module level
benchmark_df = load_sp500_data()


def fetch_and_calculate_performance_index(data_chunk):
    """Calculate Performance Index for the provided chunk of stock data"""
    try:
        performance_index_data = []
        temp_dict = {}

        # Group data by symbol
        for record in data_chunk:
            symbol = record[0]
            data = record[1:]

            if symbol not in temp_dict:
                temp_dict[symbol] = []

            temp_dict[symbol].append(data)

        # Calculate Performance Index for each symbol
        for symbol, records in temp_dict.items():
            df = pd.DataFrame(records, columns=["date", "close"])
            pi_df = calculate_performance_index(df, benchmark_df)
            latest_performance_index = (
                pi_df["PI"].dropna().iloc[-1]
                if not pi_df["PI"].dropna().empty
                else None
            )

            if pd.notna(latest_performance_index):
                performance_index_data.append(
                    (
                        symbol,
                        convert_to_float(latest_performance_index),
                        "NOW()",
                        "NOW()",
                    )
                )

        return performance_index_data
    except Exception as e:
        print(f"Error in fetch_and_calculate_performance_index: {e}")
        return []


def main(request):
    try:
        start_time = datetime.now()

        # Single database connection for the entire process
        with DBConnection() as connection:
            with connection.cursor() as cursor:
                # Get stock symbols
                stock_symbols = get_stock_symbols(cursor=cursor, only_symbols=True)
                # stock_symbols = ["AAPL"]
                print(f"Retrieved {len(stock_symbols)} stock symbols.")

                # Create generator for symbol chunks
                chunks = chunk_list(stock_symbols, 25)

                format = "%Y-%m-%d"
                one_year_ago = (datetime.now() - timedelta(days=365)).strftime(format)

                # Process each chunk using ThreadPoolExecutor
                with ThreadPoolExecutor(max_workers=5) as executor:
                    futures = []

                    # Iterate over generator and submit tasks
                    for symbols_chunk in chunks:
                        # Fetch data for current chunk
                        symbol_placeholders = ",".join(["%s"] * len(symbols_chunk))
                        query = f"""
                            SELECT s.symbol, h.date, h.close
                            FROM stocks_stock s
                            JOIN stocks_historicaldata h ON s.symbol = h.stock_id
                            WHERE s.symbol IN ({symbol_placeholders}) AND h.date >= %s
                            ORDER BY s.symbol, h.date;
                        """.strip()
                        cursor.execute(query, symbols_chunk + [one_year_ago])
                        chunk_data = cursor.fetchall()

                        # Submit Performance Index calculation to thread pool
                        futures.append(
                            executor.submit(
                                fetch_and_calculate_performance_index, chunk_data
                            )
                        )

                    # Collect results as they complete
                    performance_index_data = []
                    for future in futures:
                        chunk_result = future.result()
                        performance_index_data.extend(chunk_result)

                print(
                    f"Calculated Performance Index for {len(performance_index_data)} stocks"
                )

                if performance_index_data:
                    insert_query = """
                        INSERT INTO stocks_indicators (stock_id, performance_index, updated_at, created_at)
                        VALUES %s
                        ON CONFLICT (stock_id) DO UPDATE
                        SET
                            performance_index = EXCLUDED.performance_index,
                            updated_at = NOW();
                    """.strip()

                    execute_bulk_insert(
                        connection, cursor, insert_query, performance_index_data
                    )
                    print("Bulk insert completed.")

        end_time = datetime.now()
        execution_time = end_time - start_time
        print(f"Execution time: {execution_time}")

        return (
            f"Performance Index values inserted for {len(performance_index_data)} stocks.",
            200,
        )

    except Exception as e:
        print(f"Error in main function: {e}")
        raise


if __name__ == "__main__":
    if os.getenv("SERVER") == "development":
        freeze_support()
        main("")
