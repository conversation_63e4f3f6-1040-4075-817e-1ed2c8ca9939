{% extends 'base.html' %}

{% block title %}Market{% endblock %}
{% load custom_template_tags %}
{% block content %}
<div class="container my-4">
    <h1 class="text-center">Market Overview</h1>

    <div class="row mb-4">
        <div class="col-lg-6 mb-3">
            <div class="card bg-dark text-white">
                <div class="card-body">
                    <h5 class="card-title">CNN Fear & Greed Index</h5>
                    <p
                        class="card-text {% if fear_greed_index >= 75 %}text-danger{% elif fear_greed_index <= 30 %}text-success{% endif %}">
                        {{ fear_greed_index|floatformat:0 }}
                    </p>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-3">
            <div class="card bg-dark text-white">
                <div class="card-body">
                    <h5 class="card-title">
                        Equity Put Call Ratio
                        /
                        Total Put Call Ratio
                    </h5>
                    <p>
                        {{ equity_put_call_ratio|floatformat:2 }}
                        /
                        {{ total_put_call_ratio|floatformat:2 }}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-lg-6 mb-3">
            <div class="card bg-dark text-white">
                <div class="card-body">
                    <h5 class="card-title">CCI for SP500</h5>
                    <p
                        class="card-text {% if sp500.indicators.cci <= -150 %}text-success{% elif sp500.indicators.cci <= 130 %}text-danger{% endif %}">
                        {{ sp500.indicators.cci|floatformat:0 }}
                    </p>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-3">
            <div class="card bg-dark text-white">
                <div class="card-body">
                    <h5 class="card-title">CCI for Russell 2000</h5>
                    <p
                        class="card-text {% if russell2000.indicators.cci <= -150 %}text-success{% elif russell2000.indicators.cci <= 150 %}text-danger{% endif %}">
                        {{ russell2000.indicators.cci|floatformat:0 }}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-lg-6 mb-3">
            <div class="card bg-dark text-white">
                <div class="card-body">
                    <h5 class="card-title">VIX Indicators</h5>
                    <p class="card-text">
                        <span
                            class="{% if vix.indicators.performance_index > 0.95 %}text-danger{% endif %}">
                            PI: {{ vix.indicators.performance_index|floatformat:2 }}<br>
                        </span>
                        <span
                            class="{% if vix.indicators.ao > 0 %}text-danger{% endif %}">
                            AO: {{ vix.indicators.ao|floatformat:2 }}
                        </span>
                    </p>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-3">
            <div class="card bg-dark text-white">
                <div class="card-body">
                    <h5 class="card-title">SP500 AO Change</h5>
                    <p class="card-text">
                        {% if previous_ao is not None %}
                        <span class="{% if current_ao < previous_ao %}text-danger{% else %}text-success{% endif %}">
                            Previous: {{ previous_ao|floatformat:2 }}<br>
                            Current: {{ current_ao|floatformat:2 }}<br>
                            {% with change=current_ao|subtract:previous_ao %}
                            Change: {{ change|floatformat:2 }}
                            {% if change > 0 %}↑{% else %}↓{% endif %}
                            {% endwith %}
                        </span>
                        {% else %}
                        <span class="text-muted">No previous data available</span>
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-danger text-white">Sell Indicators</div>
                <div class="card-body bg-dark">
                    <ul>
                        <li class="{% if equity_put_call_ratio <= 0.6 %}text-danger{% endif %}">
                            Equity Put/Call Ratio:
                            {{ equity_put_call_ratio|floatformat:2 }} <i><small>(0.6 or less)</small></i>
                        </li>
                        <li class="{% if fear_greed_index >= 75 %}text-danger{% endif %}">
                            Fear/Greed Index:
                            {{ fear_greed_index|floatformat:0 }} <i><small>(Reached 75 or more)</small></i>
                        </li>
                        <li class="{% if fear_greed_index < 75 and was_above_75 %}text-danger{% endif %}">
                            Fear/Greed Index:
                            {{ fear_greed_index|floatformat:0 }} <i><small>(Below 75 after being above 75)</small></i>
                        </li>
                        <li class="{% if russell2000.indicators.cci <= 150 %}text-danger{% endif %}">
                            Russell 2000 CCI:
                            {{ russell2000.indicators.cci|floatformat:0 }} <i><small>(150 or less)</small></i>
                        </li>
                        <li class="{% if sp500.indicators.cci <= 130 %}text-danger{% endif %}">
                            SP500 CCI:
                            {{ sp500.indicators.cci|floatformat:0 }} <i><small>(130 or less)</small></i>
                        </li>
                        <li class="{% if sp500.indicators.ao >= 150 %}text-danger{% endif %}">
                            SP500 AO:
                            {{ sp500.indicators.ao|floatformat:0 }} <i><small>(Reached 150)</small></i>
                        </li>
                        <li class="{% if sp500.indicators.ao <= 100 %}text-danger{% endif %}">
                            SP500 AO:
                            {{ sp500.indicators.ao|floatformat:0 }} <i><small>(Reached 100 or less)</small></i>
                        </li>
                        <li class="{% if sp500.price < sp500.indicators.sma8 %}text-danger{% endif %}">
                            Price:
                            {{ sp500.price|floatformat:0 }}
                            SMA 8:
                            {{ sp500.indicators.sma8|floatformat:0 }} <i><small>(Price below the 8 day SMA)</small></i>
                        </li>
                        <li class="{% if sp500.price < sp500.indicators.sma16 %}text-danger{% endif %}">
                            Price:
                            {{ sp500.price|floatformat:0 }}
                            SMA 16:
                            {{ sp500.indicators.sma16|floatformat:0 }}
                            <i><small>
                                    (Price below the 16 day SMA)
                                </small></i>
                        </li>
                        <li class="{% if sp500.indicators.sma8 < sp500.indicators.sma16 %}text-danger{% endif %}">
                            SMA 8:
                            {{ sp500.indicators.sma8|floatformat:0 }}
                            SMA 16:
                            {{ sp500.indicators.sma16|floatformat:0 }}
                            <i><small>
                                    (8 day SMA crosses below 16 day SMA)
                                </small></i>
                        </li>
                        <li class="{% if cci_or_ao_condition %}text-danger{% endif %}">
                            <i><small>
                                    CCI is < 100 or Awesome is dropping by 30 points daily </small></i>
                        </li>
                        <li class="{% if sp500.indicators.rv >= 75 %}text-danger{% endif %}">
                            SP500 RV:
                            {{ sp500.indicators.rv|floatformat:0 }} <i><small>(RV is now 75 or more)</small></i>
                        </li>
                        <li class="{% if sp500.indicators.rv <= 55 %}text-danger{% endif %}">
                            SP500 RV:
                            {{ sp500.indicators.rv|floatformat:0 }} <i><small>(RV is now 55 or less)</small></i>
                        </li>
                        <li class="{% if sp500.indicators.mfi >= 65 %}text-danger{% endif %}">
                            SP500 MFI:
                            {{ sp500.indicators.mfi|floatformat:0 }} <i><small>(Money flow reached 65 or
                                    more)</small></i>
                        </li>
                        <li class="{% if sp500.indicators.mfi < 60 %}text-danger{% endif %}">
                            SP500 MFI:
                            {{ sp500.indicators.mfi|floatformat:0 }} <i><small>(Money flow is < 60)</small></i>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white">Buy Indicators</div>
                <div class="card-body bg-dark">
                    <!-- <ul class="list-unstyled"> -->
                    <ul>
                        <li class="{% if total_put_call_ratio >= 1.1 %}text-success{% endif %}">
                            Total Put/Call Ratio:
                            {{ total_put_call_ratio|floatformat:2 }} <i><small>(1.1 or more)</small></i>
                        </li>
                        <li class="{% if fear_greed_index <= 30 %}text-success{% endif %}">
                            Fear/Greed Index:
                            {{ fear_greed_index|floatformat:0 }} <i><small>(30 or less)</small></i>
                        </li>
                        <li class="{% if sp500.indicators.cci <= -150 %}text-success{% endif %}">
                            SP500 CCI: {{ sp500.indicators.cci|floatformat:0 }} <i><small>(Reached -150)</small></i>
                        </li>
                        <li class="{% if russell2000.indicators.cci <= -150 %}text-success{% endif %}">
                            Russell 2000 CCI:
                            {{ russell2000.indicators.cci|floatformat:0 }} <i><small>(Reached -150)</small></i>
                        </li>
                        <li class="{% if sp500.indicators.cci > -100 %}text-success{% endif %}">
                            SP500 CCI:
                            {{ sp500.indicators.cci|floatformat:0 }} <i><small>(>-100)</small></i>
                        </li>
                        <li class="{% if sp500.indicators.mfi <= 35 %}text-success{% endif %}">
                            SP500 MFI: {{ sp500.indicators.mfi|floatformat:0 }}
                            <i><small>(Reached 35 or lower)</small></i>
                        </li>
                        <li class="{% if sp500.indicators.ao <= -100 %}text-success{% endif %}">
                            SP500 AO:
                            {{ sp500.indicators.ao|floatformat:0 }} <i><small>(Reached -100 or lower)</small></i>
                        </li>
                        <li class="{% if price_within_3_below_16_sma %}text-success{% endif %}">
                            Price:
                            {{ sp500.price|floatformat:0 }}
                            SMA 16:
                            {{ sp500.indicators.sma16|floatformat:0 }}
                            <i><small>(Price is within 3% below 16 day SMA)</small></i>
                        </li>
                        <li class="{% if price_within_5_below_150_sma %}text-success{% endif %}">
                            Price:
                            {{ sp500.price|floatformat:0 }}
                            SMA 150:
                            {{ sp500.indicators.sma150|floatformat:0 }}
                            <i><small>
                                    (Price is within 5% below 150 day SMA)
                                </small></i>
                        </li>
                        <li class="{% if sp500.price > sp500.indicators.sma8 %}text-success{% endif %}">
                            Price:
                            {{ sp500.price|floatformat:0 }}
                            SMA 8:
                            {{ sp500.indicators.sma8|floatformat:0 }}
                            <i><small>(Price above the 8 day SMA)</small></i>
                        </li>
                        <li class="{% if sp500.price > sp500.indicators.sma16 %}text-success{% endif %}">
                            Price:
                            {{ sp500.price|floatformat:0 }}
                            SMA 16:
                            {{ sp500.indicators.sma16|floatformat:0 }}
                            <i><small>(Price above the 16 day SMA)</small></i>
                        </li>
                        <li class="{% if sp500.indicators.rv > 35 %}text-success{% endif %}">
                            SP500 RV:
                            {{ sp500.indicators.rv|floatformat:0 }} <i><small>(>35)</small></i>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}