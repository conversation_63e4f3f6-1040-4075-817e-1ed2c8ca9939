{% extends 'base.html' %}

{% block title %}Login{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card bg-dark text-white border-secondary">
            <div class="card-header bg-dark border-secondary">
                <h4 class="card-title mb-0 text-center">Login</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    {% if form.errors %}
                    <div class="alert alert-danger">
                        Invalid username or password.
                    </div>
                    {% endif %}
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" name="username" class="form-control bg-dark text-white" id="username"
                            required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" name="password" class="form-control bg-dark text-white" id="password"
                            required>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Login</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}