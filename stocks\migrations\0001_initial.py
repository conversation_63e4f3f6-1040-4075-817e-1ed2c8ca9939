# Generated by Django 5.0.6 on 2024-06-12 06:20

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Stock",
            fields=[
                (
                    "symbol",
                    models.CharField(
                        max_length=10, primary_key=True, serialize=False, unique=True
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="Indicators",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("cci", models.FloatField(blank=True, null=True)),
                ("rv", models.FloatField(blank=True, null=True)),
                ("mfi", models.FloatField(blank=True, null=True)),
                ("performance_index", models.FloatField(blank=True, null=True)),
                ("ao", models.FloatField(blank=True, null=True)),
                ("cci_overbought", models.FloatField(default=100)),
                ("cci_oversold", models.FloatField(default=-100)),
                ("rv_oversold", models.FloatField(default=30)),
                ("rv_overbought", models.FloatField(default=70)),
                ("mfi_overbought", models.FloatField(default=80)),
                ("mfi_oversold", models.FloatField(default=20)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "stock",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="indicators",
                        to="stocks.stock",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="HistoricalData",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField()),
                ("open", models.FloatField()),
                ("high", models.FloatField()),
                ("low", models.FloatField()),
                ("close", models.FloatField()),
                ("volume", models.BigIntegerField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "stock",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="historical_data",
                        to="stocks.stock",
                    ),
                ),
            ],
            options={
                "unique_together": {("stock", "date")},
            },
        ),
    ]
