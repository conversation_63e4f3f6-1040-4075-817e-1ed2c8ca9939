{% extends 'base.html' %}
{% load humanize %}
{% load custom_template_tags %}

{% block title %}{{ action }} Target{% endblock %}

{% block content %}
<div class="container my-4">
    <h1 class="text-center">{{ action }} Target</h1>

    <div class="row">
        <div class="col-12">
            <div class="card bg-dark">
                <div class="card-body">
                    {% if stock_info %}
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-dark border-secondary">
                                <div class="card-body">
                                    <h5 class="card-title text-white">Stock Information</h5>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <p class="mb-1"><small class="text-muted">Name:</small></p>
                                            <p class="text-white">{{ stock_info.name }}</p>
                                        </div>
                                        <div class="col-md-3">
                                            <p class="mb-1"><small class="text-muted">Current Price:</small></p>
                                            <p class="text-white">${{ stock_info.price|floatformat:2|intcomma }}</p>
                                        </div>
                                        <div class="col-md-3">
                                            <p class="mb-1"><small class="text-muted">Change:</small></p>
                                            <p
                                                class="{% if stock_info.change_percentage > 0 %}text-success{% elif stock_info.change_percentage < 0 %}text-danger{% else %}text-white{% endif %}">
                                                {{ stock_info.change_percentage|floatformat:2 }}%
                                            </p>
                                        </div>
                                        <div class="col-md-3">
                                            <p class="mb-1"><small class="text-muted">Sector:</small></p>
                                            <p class="text-white">{{ stock_info.sector|default:"N/A" }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <form method="POST">
                        {% csrf_token %}
                        <div class="row g-3">
                            <!-- Symbol field on the left -->
                            <div class="col-md-6">
                                <label for="{{ form.symbol.id_for_label }}" class="form-label">
                                    {{ form.symbol.label }}
                                </label>
                                {{ form.symbol|add_class:"form-control bg-dark text-white" }}
                                {% if form.symbol.help_text %}
                                <small class="form-text text-muted">{{ form.symbol.help_text }}</small>
                                {% endif %}
                                {% if form.symbol.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.symbol.errors|join:", " }}
                                </div>
                                {% endif %}
                            </div>

                            <!-- Target price field on the right -->
                            <div class="col-md-6">
                                <label for="{{ form.target_price.id_for_label }}" class="form-label">
                                    {{ form.target_price.label }}
                                </label>
                                {{ form.target_price|add_class:"form-control bg-dark text-white" }}
                                {% if form.target_price.help_text %}
                                <small class="form-text text-muted">{{ form.target_price.help_text }}</small>
                                {% endif %}
                                {% if form.target_price.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.target_price.errors|join:", " }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-12 mt-3">
                                <button type="submit" class="btn btn-primary">
                                    {% if form.instance.pk %}Save{% else %}{{ action }}{% endif %}
                                </button>
                                <a href="{% url 'portfolio_list' %}" class="btn btn-secondary">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}