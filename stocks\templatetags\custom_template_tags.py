# templatetags/querystring.py
from django import template

register = template.Library()


@register.simple_tag
def update_query_params(request, **kwargs):
    updated = request.GET.copy()
    for key, value in kwargs.items():
        updated[key] = value
    return updated.urlencode()


@register.filter(name="add_class")
def add_class(field, css_class):
    return field.as_widget(attrs={"class": css_class})


@register.filter
def subtract(value, arg):
    try:
        return float(value) - float(arg)
    except (ValueError, TypeError):
        return ""


@register.filter
def ma150_percentage(price, sma150):
    try:
        price = float(price)
        sma150 = float(sma150)
        return ((price - sma150) / sma150) * 100
    except (ValueError, TypeError, ZeroDivisionError):
        return None
