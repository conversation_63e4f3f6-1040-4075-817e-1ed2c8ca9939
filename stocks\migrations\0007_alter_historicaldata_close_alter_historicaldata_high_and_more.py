# Generated by Django 4.2.13 on 2024-06-26 07:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("stocks", "0006_savedfilter"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="historicaldata",
            name="close",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="historicaldata",
            name="high",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="historicaldata",
            name="low",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="historicaldata",
            name="open",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="historicaldata",
            name="volume",
            field=models.BigIntegerField(blank=True, null=True),
        ),
    ]
