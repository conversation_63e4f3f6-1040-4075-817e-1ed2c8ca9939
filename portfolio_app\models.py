from decimal import Decimal

from django.db import models


class PortfolioTarget(models.Model):
    stock = models.ForeignKey("stocks.Stock", on_delete=models.CASCADE)
    target_price = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    current_rank = models.IntegerField(null=True, blank=True)
    previous_rank = models.IntegerField(null=True, blank=True)

    class Meta:
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.stock.symbol} - Target: ${self.target_price}"

    @property
    def potential_gain(self):
        if self.stock.price:
            return (
                (self.target_price - Decimal(str(self.stock.price)))
                / Decimal(str(self.stock.price))
            ) * 100
        return 0

    @property
    def target_reached(self):
        if self.stock.price:
            return self.stock.price >= self.target_price
        return False

    @property
    def rank_movement(self):
        """Returns the movement in rank compared to previous position"""
        if self.previous_rank and self.current_rank:
            return self.previous_rank - self.current_rank
        return 0
