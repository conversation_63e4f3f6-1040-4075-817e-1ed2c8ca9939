# portfolio_app/views.py
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.views.generic import ListView

from stocks.models import Stock

from .forms import PortfolioTargetForm
from .models import PortfolioTarget


class PortfolioListView(LoginRequiredMixin, ListView):
    login_url = "/auth/login/"
    model = PortfolioTarget
    template_name = "portfolio_app/portfolio_list.html"
    context_object_name = "targets"

    columns = [
        {"key": "stock__symbol", "label": "Rank"},
        {"key": "stock__symbol", "label": "Symbol"},
        {"key": "stock__name", "label": "Name"},
        {"key": "stock__price", "label": "Current Price"},
        {"key": "target_price", "label": "Target Price"},
        {"key": "potential_gain", "label": "Potential Gain"},
        {"key": "stock__indicators__cci", "label": "CCI"},
        {"key": "stock__indicators__rv", "label": "RV"},
        {"key": "stock__indicators__ao", "label": "AO"},
        {"key": "stock__indicators__mfi", "label": "MFI"},
        {"key": "stock__indicators__sma150", "label": "MA 150"},
        {"key": "stock__indicators__ma_env_slope", "label": "MA Slope"},
        {"key": "stock__indicators__stoch_k", "label": "Stoch Black"},
        {"key": "stock__indicators__stoch_d", "label": "Stoch Red"},
        {"key": "rank_movement", "label": "Movement"},
        {"key": "status", "label": "Status"},
        {"key": "stock__symbol", "label": "Actions"},
    ]

    def get_queryset(self):
        # Sort by potential gain
        queryset = PortfolioTarget.objects.all()
        sort_by = self.request.GET.get("s")

        if sort_by:
            desc = False
            if sort_by.startswith("-"):
                sort_by = sort_by[1:]
                desc = True

            # Handle special cases
            if sort_by == "potential_gain":
                # Convert to list to sort by property
                sorted_targets = sorted(
                    queryset, key=lambda x: x.potential_gain, reverse=desc
                )
                return sorted_targets
            elif sort_by == "rank_movement":
                sorted_targets = sorted(
                    queryset, key=lambda x: x.rank_movement, reverse=desc
                )
                return sorted_targets
            elif sort_by == "stock__indicators__sma150":
                # Sort by MA150 percentage
                def ma150_key(target):
                    if target.stock.price and target.stock.indicators.sma150:
                        return (
                            (target.stock.price - target.stock.indicators.sma150)
                            / target.stock.indicators.sma150
                            * 100
                        )
                    return float("-inf") if desc else float("inf")

                sorted_targets = sorted(queryset, key=ma150_key, reverse=desc)
                return sorted_targets
            else:
                # For database fields, use order_by
                order_by = f"{'-' if desc else ''}{sort_by}"
                return queryset.order_by(order_by)

        # Default sort by potential gain
        return sorted(queryset, key=lambda x: x.potential_gain, reverse=True)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Create a comma-separated list of symbols for copying
        context["symbols_list"] = ",".join(
            [target.stock.symbol for target in context["targets"]]
        )
        context["columns"] = self.columns
        return context


@login_required
def add_target(request):
    initial_data = {}
    stock_info = None

    if request.method == "GET" and "symbol" in request.GET:
        symbol = request.GET["symbol"].upper()
        initial_data["symbol"] = symbol
        try:
            stock_info = Stock.objects.get(symbol=symbol)
        except Stock.DoesNotExist:
            pass

    if request.method == "POST":
        form = PortfolioTargetForm(request.POST)
        if form.is_valid():
            symbol = form.cleaned_data["symbol"].upper()
            try:
                stock = Stock.objects.get(symbol=symbol)
                # Check if target already exists for this stock
                if PortfolioTarget.objects.filter(stock=stock).exists():
                    messages.error(request, f"Target already exists for {symbol}")
                    return redirect("portfolio_add")

                target = form.save(commit=False)
                target.stock = stock
                target.save()
                messages.success(request, f"Added target for {symbol}")
                return redirect("portfolio_list")
            except Stock.DoesNotExist:
                messages.error(request, f"Stock {symbol} not found")
                return redirect("portfolio_add")
    else:
        form = PortfolioTargetForm(initial=initial_data)

    return render(
        request,
        "portfolio_app/portfolio_form.html",
        {"form": form, "action": "Add", "stock_info": stock_info},
    )


@login_required
def edit_target(request, pk):
    target = get_object_or_404(PortfolioTarget, pk=pk)
    if request.method == "POST":
        form = PortfolioTargetForm(request.POST, instance=target)
        if form.is_valid():
            form.save()
            messages.success(request, f"Updated target for {target.stock.symbol}")
            return redirect("portfolio_list")
    else:
        form = PortfolioTargetForm(instance=target)

    return render(
        request,
        "portfolio_app/portfolio_form.html",
        {"form": form, "action": "Edit", "target": target},
    )


@login_required
def delete_target(request, pk):
    target = get_object_or_404(PortfolioTarget, pk=pk)
    symbol = target.stock.symbol
    target.delete()
    messages.success(request, f"Deleted target for {symbol}")
    return redirect("portfolio_list")


# Optional: API endpoint for copying symbols
def get_symbols(request):
    symbols = PortfolioTarget.objects.values_list("stock__symbol", flat=True)
    return JsonResponse({"symbols": ",".join(symbols)})
