import os
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta

import pandas as pd
from common.utils import (
    DBConnection,
    calculate_sma,
    chunk_list,
    convert_to_float,
    execute_bulk_insert,
    get_stock_symbols,
)


def fetch_and_calculate_sma(data_chunk):
    """Calculate SMA for a chunk of stock data"""
    try:
        sma_data = []
        temp_dict = {}

        # Group data by symbol
        for record in data_chunk:
            symbol = record[0]
            data = record[1:]

            if symbol not in temp_dict:
                temp_dict[symbol] = []
            temp_dict[symbol].append(data)

        # Calculate SMA for each symbol
        for symbol, records in temp_dict.items():
            df = pd.DataFrame(records, columns=["date", "close"])
            df["sma8"] = calculate_sma(df, period=8)
            df["sma16"] = calculate_sma(df, period=16)
            df["sma150"] = calculate_sma(df, period=150)

            latest_sma8 = (
                df["sma8"].dropna().iloc[-1] if not df["sma8"].dropna().empty else None
            )
            latest_sma16 = (
                df["sma16"].dropna().iloc[-1]
                if not df["sma16"].dropna().empty
                else None
            )
            latest_sma150 = (
                df["sma150"].dropna().iloc[-1]
                if not df["sma150"].dropna().empty
                else None
            )

            if pd.notna(latest_sma8) and pd.notna(latest_sma16):
                sma_data.append(
                    (
                        symbol,
                        convert_to_float(latest_sma8),
                        convert_to_float(latest_sma16),
                        convert_to_float(latest_sma150),
                        "NOW()",
                        "NOW()",
                    )
                )

        return sma_data
    except Exception as e:
        print(f"Error in fetch_and_calculate_sma: {e}")
        return []


def main(request):
    try:
        with DBConnection() as connection:
            with connection.cursor() as cursor:
                # Get stock symbols
                stock_symbols = get_stock_symbols(cursor=cursor, only_symbols=True)
                # stock_symbols = ["AAPL", "NEXT"]
                print(f"Retrieved {len(stock_symbols)} stock symbols.")

                # Create generator for symbol chunks
                chunks = chunk_list(stock_symbols, 25)

                format = "%Y-%m-%d"
                one_year_ago = (datetime.now() - timedelta(days=365)).strftime(format)

                # Process each chunk using ThreadPoolExecutor
                with ThreadPoolExecutor(max_workers=5) as executor:
                    futures = []

                    # Iterate over generator and submit tasks
                    for symbols_chunk in chunks:
                        # Fetch data for current chunk
                        symbol_placeholders = ",".join(["%s"] * len(symbols_chunk))
                        query = f"""
                            SELECT s.symbol, h.date, h.close
                            FROM stocks_stock s
                            JOIN stocks_historicaldata h ON s.symbol = h.stock_id
                            WHERE s.symbol IN ({symbol_placeholders}) AND h.date >= %s
                            ORDER BY s.symbol, h.date;
                        """.strip()
                        cursor.execute(query, symbols_chunk + [one_year_ago])
                        chunk_data = cursor.fetchall()

                        # Submit SMA calculation to thread pool
                        futures.append(
                            executor.submit(fetch_and_calculate_sma, chunk_data)
                        )

                    # Collect results as they complete
                    sma_data = []
                    for future in futures:
                        chunk_result = future.result()
                        sma_data.extend(chunk_result)

                print(f"Calculated SMA for {len(sma_data)} stocks")

                if sma_data:
                    insert_query = """
                        INSERT INTO stocks_indicators (stock_id, sma8, sma16, sma150, updated_at, created_at)
                        VALUES %s
                        ON CONFLICT (stock_id) DO UPDATE
                        SET
                            sma8 = EXCLUDED.sma8,
                            sma16 = EXCLUDED.sma16,
                            sma150 = EXCLUDED.sma150,
                            updated_at = NOW();
                    """.strip()

                    execute_bulk_insert(connection, cursor, insert_query, sma_data)
                    print("Bulk insert completed.")

        return f"SMA values inserted for {len(sma_data)} stocks.", 200

    except Exception as e:
        print(f"Error in main function: {e}")
        raise


if __name__ == "__main__":
    main("")
