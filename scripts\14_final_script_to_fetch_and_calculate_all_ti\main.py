import gc
import logging
import os
import time
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta

import pandas as pd
import requests
from common.firestore_utils import (
    bulk_update_sp500_ao_history,
    update_sp500_ao_in_firestore,
)
from common.utils import (
    APIRateTracker,
    DBConnection,
    MockRequest,
    calculate_awesome_oscillator,
    calculate_cci,
    calculate_ma_envelope_slope,
    calculate_mfi,
    calculate_performance_index,
    calculate_relative_volatility,
    calculate_sma,
    calculate_stochastic,
    chunk_list,
    convert_numpy_bool,
    convert_to_float,
    execute_bulk_insert,
    get_stock_symbols,
    load_sp500_data,
)

CONFIG = {
    "MAX_WORKERS": 4,
    "SP500_SYMBOL": "^GSPC",
    "CHUNK_SIZE": 75,
    "BATCH_TIME": 60,
}
rate_tracker = APIRateTracker()


def setup_logging():
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )


# Call this at the start of your script
setup_logging()


BENCHMARK_DF = load_sp500_data()


def process_in_batches(stock_symbols):
    total_stocks = len(stock_symbols)
    # 300 stocks/batch
    batch_size = CONFIG["MAX_WORKERS"] * CONFIG["CHUNK_SIZE"]
    logging.info(f"Processing {total_stocks} stocks in batches of {batch_size}")

    for i in range(0, total_stocks, batch_size):
        batch = stock_symbols[i : i + batch_size]
        chunks = list(chunk_list(batch, CONFIG["CHUNK_SIZE"]))

        with ThreadPoolExecutor(max_workers=CONFIG["MAX_WORKERS"]) as executor:
            futures = [executor.submit(process_chunk, chunk) for chunk in chunks]
            processed = [f.result() for f in futures]

        # Wait for next minute if not last batch
        if i + batch_size < total_stocks:
            time.sleep(CONFIG["BATCH_TIME"])

        yield processed


def fetch_single_stock(symbol):
    try:
        wait_time = rate_tracker.wait_time()
        if wait_time > 0:
            time.sleep(wait_time)
        rate_tracker.add_call()

        api_key = os.getenv("FMP_API_KEY")
        if not api_key:
            raise ValueError("FMP_API_KEY not found in environment")

        from_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")
        url = f"https://financialmodelingprep.com/api/v3/historical-price-full/{symbol}?apikey={api_key}&from={from_date}"

        response = requests.get(url)
        if response.status_code != 200:
            logging.warning(f"No data found for {symbol}")
            return [], None

        data = response.json()
        hist_data = data.get("historical", [])
        if not hist_data:
            return [], None

        sp500_data = None
        if symbol == CONFIG["SP500_SYMBOL"] and hist_data:
            latest = hist_data[0]
            sp500_data = (
                latest["close"],
                latest["changePercent"],
            )

        return hist_data, sp500_data

    except requests.exceptions.RequestException as e:
        logging.error(f"Network error fetching {symbol}: {str(e)}")
        return [], None
    except Exception as e:
        logging.error(f"Unexpected error fetching {symbol}: {str(e)}")
        return [], None


def calculate_indicators(symbol, symbol_data):
    try:
        if not symbol_data:
            return None

        df = pd.DataFrame(symbol_data)

        if df.empty:
            return None

        df["date"] = pd.to_datetime(df["date"])
        df = df.sort_values("date", ascending=True).reset_index(drop=True)

        df["cci"] = calculate_cci(df)
        df["rv"] = calculate_relative_volatility(df)
        df["mfi"] = calculate_mfi(df)
        mfi_series = df["mfi"].dropna()
        latest_mfi = mfi_series.iloc[-1] if not mfi_series.empty else None
        df["ao"] = calculate_awesome_oscillator(df)
        df["sma8"] = calculate_sma(df, period=8)
        df["sma16"] = calculate_sma(df, period=16)
        df["sma150"] = calculate_sma(df, period=150)
        ma_env_slope = calculate_ma_envelope_slope(df)
        df = calculate_stochastic(df)

        if BENCHMARK_DF.empty:
            logging.info("Warning: Empty benchmark data")
        else:
            df = calculate_performance_index(df, BENCHMARK_DF)

        latest = df.iloc[-1]

        # Handle SP500 Firestore updates
        if symbol == CONFIG["SP500_SYMBOL"]:
            try:
                # Get latest AO value and date
                latest_ao = df["ao"].dropna().iloc[-1]
                latest_date = df["date"].iloc[-1]

                # Update latest value
                update_sp500_ao_in_firestore(
                    ao_value=convert_to_float(latest_ao), date=latest_date
                )

                # Prepare data for bulk update
                # sp500_df = df[["date", "ao"]].copy()
                # sp500_df["symbol"] = symbol

                # # Update historical values
                # bulk_update_sp500_ao_history(sp500_df, days=30)

                logging.info("Updated SP500 AO values in Firestore")
            except Exception as e:
                logging.error(f"Error updating SP500 AO in Firestore: {str(e)}")

        return (
            symbol,
            convert_to_float(latest.get("cci")),
            convert_to_float(latest.get("rv")),
            convert_to_float(latest_mfi),
            convert_to_float(latest.get("ao")),
            convert_to_float(latest.get("sma8")),
            convert_to_float(latest.get("sma16")),
            convert_to_float(latest.get("sma150")),
            convert_to_float(ma_env_slope),
            convert_to_float(latest.get("smi")),
            convert_to_float(latest.get("smi_signal")),
            convert_numpy_bool(latest.get("smi_crossover")),
            convert_to_float(latest.get("PI")),
            "NOW()",
            "NOW()",
        )

    except Exception as e:
        logging.error(f"Error calculating indicators: {str(e)}")
        return None
    finally:
        del df
        gc.collect()


def process_chunk(symbols):
    chunk_data = []
    for symbol in symbols:
        historical_data, sp500_data = fetch_single_stock(symbol)
        if historical_data:
            chunk_data.append(
                {
                    "symbol": symbol,
                    "historical_data": historical_data,
                    "sp500_data": sp500_data,
                }
            )
    return chunk_data


def main(request):
    try:
        setup_logging()
        start_time = time.time()

        logging.info("Starting main function execution")

        # Get parameters from request
        request_json = request.get_json(silent=True)
        part = int(request_json.get("part", 1))
        divide_by = int(request_json.get("divide_by", 1))

        logging.info(f"Request parameters: divide_by={divide_by}, part={part}")

        with DBConnection() as connection:
            with connection.cursor() as cursor:
                stock_symbols = get_stock_symbols(cursor=cursor, only_symbols=True)

        # stock_symbols = ["AAPL", "^GSPC", "^RUT", "^VIX", "TSLA"]
        logging.info(f"Retrieved {len(stock_symbols)} stock symbols")

        # Better partition calculation
        total_symbols = len(stock_symbols)
        base_size = total_symbols // divide_by
        remainder = total_symbols % divide_by

        start_idx = (part - 1) * base_size + min(part - 1, remainder)
        end_idx = start_idx + base_size + (1 if part <= remainder else 0)

        logging.info(
            f"Processing partition {part}/{divide_by}: symbols[{start_idx}:{end_idx}]"
        )

        # Get partitioned symbols
        stock_symbols = stock_symbols[start_idx:end_idx]
        logging.info(f"Processing {len(stock_symbols)} symbols in this partition")

        all_indicators = []
        processed_chunks = []

        logging.info("Starting batch processing...")
        for batch_result in process_in_batches(stock_symbols):
            processed_chunks.extend(
                [item for sublist in batch_result for item in sublist]
            )
            logging.info(f"Processed batch: {len(processed_chunks)} stocks so far")

        with DBConnection() as connection:
            with connection.cursor() as cursor:
                for chunk_data in processed_chunks:
                    symbol = chunk_data["symbol"]
                    historical_data = chunk_data["historical_data"]
                    sp500_data = chunk_data["sp500_data"]

                    if sp500_data and symbol == CONFIG["SP500_SYMBOL"]:
                        try:
                            cursor.execute(
                                """UPDATE stocks_stock
                                SET price = %s, change_percentage = %s, updated_at = NOW()
                                WHERE symbol = %s;""".strip(),
                                (*sp500_data, CONFIG["SP500_SYMBOL"]),
                            )
                            connection.commit()
                        except Exception as e:
                            logging.error(f"Error updating SP500 data: {str(e)}")
                            connection.rollback()

                    indicators = calculate_indicators(symbol, historical_data)
                    if indicators:
                        all_indicators.append(indicators)

                if all_indicators:
                    try:
                        insert_query = """INSERT INTO stocks_indicators
                            (stock_id, cci, rv, mfi, ao, sma8, sma16, sma150,
                            ma_env_slope, stoch_k, stoch_d, stoch_crossover,
                            performance_index, updated_at, created_at)
                            VALUES %s
                            ON CONFLICT (stock_id) DO UPDATE SET
                                cci = EXCLUDED.cci,
                                rv = EXCLUDED.rv,
                                mfi = EXCLUDED.mfi,
                                ao = EXCLUDED.ao,
                                sma8 = EXCLUDED.sma8,
                                sma16 = EXCLUDED.sma16,
                                sma150 = EXCLUDED.sma150,
                                ma_env_slope = EXCLUDED.ma_env_slope,
                                stoch_k = EXCLUDED.stoch_k,
                                stoch_d = EXCLUDED.stoch_d,
                                stoch_crossover = EXCLUDED.stoch_crossover,
                                performance_index = EXCLUDED.performance_index,
                                updated_at = NOW();""".strip()
                        execute_bulk_insert(
                            connection, cursor, insert_query, all_indicators
                        )
                        logging.info(
                            f"Updated indicators for {len(all_indicators)} stocks"
                        )
                    except Exception as e:
                        logging.error(f"Error inserting indicators: {str(e)}")
                        connection.rollback()

        execution_time = time.time() - start_time
        logging.info(
            f"Completed in {execution_time:.2f}s. Processed {len(all_indicators)} stocks."
        )
        return f"Updated indicators for {len(all_indicators)} stocks.", 200

    except Exception as e:
        logging.error(f"Critical error in main function: {str(e)}")
        return f"Error processing data: {str(e)}", 500


if __name__ == "__main__" and os.getenv("SERVER") == "development":
    main(MockRequest())
