#!/bin/bash

CLOUD_SQL_INSTANCE="keen-petal-426103-n7:us-central1:yahoo-stock-screener-db"

# Function to deploy a cloud function
deploy_function() {
    local function_name=$1
    local function_dir=$2
    local generation=$3
    local cron_syntax=${4:-""}
    local message_bodies=${5:-""}
    
    # Copy the common folder and requirements.txt to the function directory
    cp -r scripts/common $function_dir/
    cp requirements.txt $function_dir/

    # Deploying as a Cloud Function (Gen1)
    echo "Deploying $function_name as Cloud Function $generation..."

    # Conditionally set Docker registry option if deploying to Gen1
    docker_registry_option=""
    if [ "$generation" = "no-gen2" ]; then
        docker_registry_option="--docker-registry=artifact-registry"
    fi

    # Set timeout based on generation
    timeout_value="540s"
    if [ "$generation" = "gen2" ]; then
        timeout_value="3600s"
    fi


    gcloud functions deploy $function_name \
        --region=us-central1 \
        --source=$function_dir \
        --runtime python311 --trigger-http --allow-unauthenticated \
        --set-env-vars DB_HOST=${DB_HOST},DB_PWD=${DB_PWD},DB_USER=${DB_USER},DB_NAME=${DB_NAME},BASE64_STR=${BASE64_STR},MAILJET_API_KEY=${MAILJET_API_KEY},MAILJET_API_SECRET=${MAILJET_API_SECRET},MAILJET_SENDER_EMAIL=${MAILJET_SENDER_EMAIL},FMP_API_KEY=${FMP_API_KEY} \
        --entry-point=main \
        --timeout=$timeout_value \
        --memory=4GiB \
        --cpu=2 \
        --concurrency=5 \
        --min-instances=0 \
        --max-instances=50 \
        --$generation \
        $docker_registry_option

    # Create Cloud Scheduler jobs for each message body (if provided)
    if [[ -n "$cron_syntax" && -n "$message_bodies" ]]; then
        local job_name_prefix="${function_name}-job-"
        
        # Convert message bodies into an array, splitting on '|'
        IFS='|' read -r -a message_array <<< "$message_bodies"

        for job_count in "${!message_array[@]}"; do 
            message_body="${message_array[$job_count]}"
            job_name="${job_name_prefix}${job_count}"
            echo "Creating Cloud Scheduler job $job_name for $function_name with message body: $message_body..."

            gcloud scheduler jobs create http $job_name \
                --schedule="$cron_syntax" \
                --time-zone="America/New_York" \
                --uri="https://us-central1-${PROJECT_ID}.cloudfunctions.net/$function_name" \
                --http-method="POST" \
                --headers="Content-Type=application/json" \
                --message-body="$message_body" \
                --location="us-central1" \
                --oidc-service-account-email="${SERVICE_ACCOUNT_EMAIL}" \
                --oidc-token-audience="https://us-central1-${PROJECT_ID}.cloudfunctions.net/$function_name" \
                --quiet \
                --attempt-deadline=30m

        done
    else
        echo "Skipping Cloud Scheduler job creation for $function_name (no cron syntax or message bodies provided)."
    fi

    # Clean up: remove the common folder and requirements.txt from the function directory
    rm -rf $function_dir/common
    rm -f $function_dir/requirements.txt
}

if git diff --name-only $GITHUB_BEFORE $GITHUB_SHA | grep -q 'scripts/1_update_tickers/'; then
    # Set this margin for 15 minutes
    deploy_function "update_tickers_function" "scripts/1_update_tickers" "no-gen2" "*/10 9-16 * * 1-5" '{"hello": "world"}'
fi
if git diff --name-only $GITHUB_BEFORE $GITHUB_SHA | grep -q 'scripts/14_final_script_to_fetch_and_calculate_all_ti/'; then
    # Set this margin for 15 minutes
    deploy_function "final_script_to_fetch_and_calculate_all_ti_function" "scripts/14_final_script_to_fetch_and_calculate_all_ti" "gen2" "11 16 * * 1-5" '{"part": 1, "divide_by": 2}|{"part": 2, "divide_by": 2}'
fi
if git diff --name-only $GITHUB_BEFORE $GITHUB_SHA | grep -q 'scripts/8_update_signal_scores/'; then
    # Set this margin for 1 hour gen 2
    deploy_function "update_signal_scores_function" "scripts/8_update_signal_scores" "gen2" "20 17 * * 1-5" '{"hello": "world"}'
fi
if git diff --name-only $GITHUB_BEFORE $GITHUB_SHA | grep -q 'scripts/10_update_fear_greed_index/'; then
    # Set this margin for 1 hour gen 2
    deploy_function "update_fear_greed_index_function" "scripts/10_update_fear_greed_index" "no-gen2" "30 16,17,18 * * 1-5" '{"hello": "world"}'
fi