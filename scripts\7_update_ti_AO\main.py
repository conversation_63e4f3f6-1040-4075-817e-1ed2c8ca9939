import logging
import os
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor
from datetime import datetime, timed<PERSON><PERSON>
from multiprocessing import freeze_support
from typing import List, Optional, Tuple

import pandas as pd
from common.utils import (
    DBConnection,
    base64_to_dict,
    calculate_awesome_oscillator,
    chunk_list,
    convert_to_float,
    execute_bulk_insert,
    get_stock_symbols,
)
from google.cloud import firestore
from google.oauth2 import service_account

# Configuration
CONFIG = {
    "CHUNK_SIZE": 25,
    "MAX_WORKERS": 5,
    "DAYS_LOOKBACK": 365,
    "SP500_SYMBOL": "^GSPC",
    "DB_NAME": "raja-db",
}


def setup_logging():
    """Configure logging for the application"""
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )


def calculate_ao_for_symbol(group_df: pd.DataFrame) -> Optional[Tuple[str, float]]:
    """Calculate AO for a single symbol"""
    group_df["ao"] = calculate_awesome_oscillator(group_df)
    ao_values = group_df["ao"].dropna()
    if ao_values.empty:
        return None

    latest_ao = ao_values.iloc[-1]
    if pd.isna(latest_ao):
        return None

    return (group_df["symbol"].iloc[0], convert_to_float(latest_ao))


def update_sp500_ao_in_firestore(ao_value: float, date: datetime):
    """Update SP500 AO value in Firestore"""
    service_account_base64_str = os.getenv("BASE64_STR")
    if not service_account_base64_str:
        raise ValueError("Firestore credentials not found")

    service_account_info = base64_to_dict(service_account_base64_str)
    credentials = service_account.Credentials.from_service_account_info(
        service_account_info
    )
    db = firestore.Client(
        credentials=credentials,
        project=service_account_info["project_id"],
        database=CONFIG["DB_NAME"],
    )

    doc_ref = db.collection("sp500_ao").document(date.strftime("%Y-%m-%d"))
    doc_ref.set(
        {"ao_value": ao_value, "timestamp": firestore.SERVER_TIMESTAMP},
        merge=True,
    )
    logging.info(f"Updated SP500 AO value for {date.strftime('%Y-%m-%d')}")


def fetch_and_calculate_ao(data_chunk):
    """Calculate AO values for a chunk of stock data"""
    try:
        ao_data = []
        temp_dict = {}

        # Group data by symbol
        for record in data_chunk:
            symbol = record[0]
            data = record[1:]

            if symbol not in temp_dict:
                temp_dict[symbol] = []
            temp_dict[symbol].append(data)

        # Calculate AO for each symbol
        for symbol, records in temp_dict.items():
            df = pd.DataFrame(records, columns=["date", "high", "low"])
            df["symbol"] = symbol

            result = calculate_ao_for_symbol(df)
            if result:
                symbol, ao_value = result
                ao_data.append((symbol, (ao_value,), "NOW()", "NOW()"))

                # Update Firestore for SP500
                if symbol == CONFIG["SP500_SYMBOL"]:
                    latest_date = df["date"].max()
                    update_sp500_ao_in_firestore(ao_value, latest_date)

                    # # Create DataFrame with required columns
                    # sp500_df = df.copy()
                    # sp500_df["ao"] = calculate_awesome_oscillator(sp500_df)
                    # sp500_df["symbol"] = symbol
                    # sp500_df = sp500_df[["symbol", "date", "ao"]].dropna()
                    # bulk_update_sp500_ao_history(sp500_df, 30)

        return ao_data
    except Exception as e:
        logging.error(f"Error in fetch_and_calculate_ao: {e}")
        return []


def main(request) -> Tuple[str, int]:
    try:
        setup_logging()
        start_time = datetime.now()

        # Single database connection for the entire process
        with DBConnection() as connection:
            with connection.cursor() as cursor:
                # Get stock symbols
                # stock_symbols = get_stock_symbols(cursor=cursor, only_symbols=True)
                stock_symbols = ["AAPL", "^GSPC"]
                if not stock_symbols:
                    raise ValueError("No stock symbols retrieved")

                logging.info(f"Retrieved {len(stock_symbols)} stock symbols")

                # Create generator for symbol chunks
                chunks = chunk_list(stock_symbols, CONFIG["CHUNK_SIZE"])

                # Calculate start date
                start_date = (
                    datetime.now() - timedelta(days=CONFIG["DAYS_LOOKBACK"])
                ).strftime("%Y-%m-%d")

                # Process each chunk using ThreadPoolExecutor
                with ThreadPoolExecutor(max_workers=CONFIG["MAX_WORKERS"]) as executor:
                    futures = []

                    # Iterate over generator and submit tasks
                    for symbols_chunk in chunks:
                        # Fetch data for current chunk
                        symbol_placeholders = ",".join(["%s"] * len(symbols_chunk))
                        query = f"""
                            SELECT s.symbol, h.date, h.high, h.low
                            FROM stocks_stock s
                            JOIN stocks_historicaldata h ON s.symbol = h.stock_id
                            WHERE s.symbol IN ({symbol_placeholders}) AND h.date >= %s
                            ORDER BY s.symbol, h.date;
                        """.strip()
                        cursor.execute(query, symbols_chunk + [start_date])
                        chunk_data = cursor.fetchall()

                        # Submit AO calculation to thread pool
                        futures.append(
                            executor.submit(fetch_and_calculate_ao, chunk_data)
                        )

                    # Collect results as they complete
                    ao_data = []
                    for future in futures:
                        chunk_result = future.result()
                        ao_data.extend(chunk_result)

                logging.info(f"Calculated AO for {len(ao_data)} stocks")

                if ao_data:
                    insert_query = """
                        INSERT INTO stocks_indicators (stock_id, ao, updated_at, created_at)
                        VALUES %s
                        ON CONFLICT (stock_id) DO UPDATE
                        SET
                            ao = EXCLUDED.ao,
                            updated_at = NOW();
                    """.strip()

                    execute_bulk_insert(connection, cursor, insert_query, ao_data)
                    logging.info("Bulk insert completed")

        execution_time = datetime.now() - start_time
        logging.info(f"Processing completed in {execution_time}")

        return f"AO values updated for {len(ao_data)} stocks", 200

    except Exception as e:
        logging.error(f"Error in main function: {e}")
        raise


def bulk_update_sp500_ao_history(df: pd.DataFrame, days: int = 30):
    """
    Bulk update SP500 AO values in Firestore for the last specified days

    Args:
        df: DataFrame with 'date' and 'ao' columns
        days: Number of days of history to update (default: 30)

    Returns:
        int: Number of records updated
    """
    # Get credentials and initialize Firestore
    service_account_base64_str = os.getenv("BASE64_STR")
    if not service_account_base64_str:
        raise ValueError("Firestore credentials not found")

    service_account_info = base64_to_dict(service_account_base64_str)
    credentials = service_account.Credentials.from_service_account_info(
        service_account_info
    )
    db = firestore.Client(
        credentials=credentials,
        project=service_account_info["project_id"],
        database="raja-db",
    )

    # Convert cutoff_date to date for comparison
    cutoff_date = (datetime.now() - timedelta(days=days)).date()

    # Ensure df['date'] is date type for comparison
    if isinstance(df["date"].iloc[0], datetime):
        df["date"] = df["date"].dt.date

    # Filter for SP500 and last N days
    mask = (df["symbol"] == "^GSPC") & (df["date"] >= cutoff_date)

    recent_data = df[mask].copy()

    if recent_data.empty:
        logging.warning("No recent SP500 data found")
        return 0

    # Sort by date to ensure chronological order
    recent_data = recent_data.sort_values("date")

    # Prepare batch
    batch = db.batch()
    count = 0

    # Create batches of 500 operations (Firestore limit)
    for _, row in recent_data.iterrows():
        doc_ref = db.collection("sp500_ao").document(row["date"].strftime("%Y-%m-%d"))
        batch.set(
            doc_ref,
            {"ao_value": float(row["ao"]), "timestamp": firestore.SERVER_TIMESTAMP},
            merge=True,
        )
        count += 1

        # Commit every 500 operations and start new batch
        if count % 500 == 0:
            batch.commit()
            batch = db.batch()

    # Commit any remaining operations
    if count % 500 != 0:
        batch.commit()

    logging.info(f"Updated {count} SP500 AO records in Firestore")
    return count


if __name__ == "__main__" and os.getenv("SERVER") == "development":
    freeze_support()
    main("")
