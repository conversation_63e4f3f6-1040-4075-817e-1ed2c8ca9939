import os
from datetime import datetime

from common.utils import logging
from mailjet_rest import Client


def check_and_send_target_alerts(connection):
    """Check for stocks that reached target price and send email alerts"""
    logging.info("Starting target price check")

    query = """
    SELECT
        pt.id,
        s.symbol,
        s.name,
        s.price as current_price,
        pt.target_price,
        s.sector,
        s.change_percentage,
        ROUND(((s.price - pt.target_price) / pt.target_price * 100)::numeric, 2) as achieved_gain,
        s.avg_volume,
        i.ma_env_slope,
        i.stoch_k,
        i.stoch_d
    FROM portfolio_app_portfoliotarget pt
    JOIN stocks_stock s ON pt.stock_id = s.symbol
    LEFT JOIN stocks_indicators i ON s.symbol = i.stock_id
    WHERE s.price >= pt.target_price
    AND s.price IS NOT NULL
    ORDER BY achieved_gain DESC;
    """

    with connection.cursor() as cursor:
        cursor.execute(query)
        reached_targets = cursor.fetchall()

        current_time = datetime.now().strftime("%b %d, %Y at %I:%M %p")

        if not reached_targets:
            logging.info("No target prices reached")
            # send_email(
            #     subject=f"Testing Email: No Stocks Reached Target Price - {current_time}",
            #     html_content="<h2>No stocks reached the target price for today.</h2><p>This is an automated alert from your Stock Screener.</p>",
            #     text_content="No stocks reached the target price for today.",
            # )
        else:
            html_content = create_email_html(reached_targets)
            text_content = create_text_content(reached_targets)
            send_email(
                subject=f"Testing Email: Stock Alert: {len(reached_targets)} Stocks Reached Target Price - {current_time}",
                html_content=html_content,
                text_content=text_content,
            )


def send_email(subject, html_content, text_content):
    """Send an email using Mailjet"""
    mailjet = Client(
        auth=(os.getenv("MAILJET_API_KEY"), os.getenv("MAILJET_API_SECRET")),
        version="v3.1",
    )
    email_data = {
        "Messages": [
            {
                "From": {
                    "Email": os.getenv("MAILJET_SENDER_EMAIL"),
                    "Name": "Stock Vital Signs",
                },
                "To": [
                    {"Email": "<EMAIL>", "Name": "Raja Virk"},
                    {"Email": "<EMAIL>", "Name": "Huzaifa Zahoor"},
                ],
                "Subject": subject,
                "HTMLPart": html_content,
                "TextPart": text_content,
            }
        ]
    }

    result = mailjet.send.create(data=email_data)
    if result.status_code == 200:
        logging.info(f"Successfully sent email: {subject}")
    else:
        logging.error(f"Failed to send email: {result.reason}")


def create_email_html(reached_targets):
    """Create HTML content for email"""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; }
            .stock-list { margin: 20px 0; }
            .stock-item {
                padding: 10px;
                margin-bottom: 10px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            .gain-positive { color: green; }
            .gain-negative { color: red; }
        </style>
    </head>
    <body>
        <h2>Stocks That Reached Target Price</h2>
        <div class="stock-list">
    """

    for target in reached_targets:
        gain_class = "gain-positive" if target[7] >= 0 else "gain-negative"
        html += f"""
        <div class="stock-item">
            <h3>{target[1]} - {target[2]}</h3>
            <ul>
                <li>Current Price: ${target[3]:.2f}</li>
                <li>Target Price: ${target[4]:.2f}</li>
                <li>Achieved Gain: <span class="{gain_class}">{target[7]}%</span></li>
                <li>Sector: {target[5] or 'N/A'}</li>
                <li>Daily Change: {target[6]:.2f}%</li>
                <li>MA Slope: {target[9]:.2f}</li>
                <li>Stochastic Black: {target[10]:.0f}</li>
                <li>Stochastic Red: {target[11]:.0f}</li>
            </ul>
        </div>
        """

    html += """
        </div>
        <p>This is an automated alert from your Stock Screener.</p>
    </body>
    </html>
    """
    return html


def create_text_content(reached_targets):
    """Create plain text content for email"""
    text = "Stocks That Reached Target Price:\n\n"

    for target in reached_targets:
        text += f"""
        {target[1]} - {target[2]}
        - Current Price: ${target[3]:.2f}
        - Target Price: ${target[4]:.2f}
        - Achieved Gain: {target[7]}%
        - Sector: {target[5] or 'N/A'}
        - Daily Change: {target[6]:.2f}%
        - MA Slope: {target[9]:.2f}
        - Stochastic Black/Red: {target[10]:.0f}/{target[11]:.0f}
        """.strip()
    return text
