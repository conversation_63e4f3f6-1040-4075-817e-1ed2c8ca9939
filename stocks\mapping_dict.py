from django.db.models import F

PRICE_FILTER_MAPPING = {
    "u1": {"price__lt": 1},
    "u2": {"price__lt": 2},
    "u3": {"price__lt": 3},
    "u4": {"price__lt": 4},
    "u5": {"price__lt": 5},
    "u7": {"price__lt": 7},
    "u10": {"price__lt": 10},
    "u15": {"price__lt": 15},
    "u20": {"price__lt": 20},
    "u30": {"price__lt": 30},
    "u40": {"price__lt": 40},
    "u50": {"price__lt": 50},
    "o1": {"price__gt": 1},
    "o2": {"price__gt": 2},
    "o3": {"price__gt": 3},
    "o4": {"price__gt": 4},
    "o5": {"price__gt": 5},
    "o7": {"price__gt": 7},
    "o10": {"price__gt": 10},
    "o15": {"price__gt": 15},
    "o20": {"price__gt": 20},
    "o30": {"price__gt": 30},
    "o40": {"price__gt": 40},
    "o50": {"price__gt": 50},
    "o60": {"price__gt": 60},
    "o70": {"price__gt": 70},
    "o80": {"price__gt": 80},
    "o90": {"price__gt": 90},
    "o100": {"price__gt": 100},
    "1to5": {"price__gte": 1, "price__lte": 5},
    "1to10": {"price__gte": 1, "price__lte": 10},
    "1to20": {"price__gte": 1, "price__lte": 20},
    "5to10": {"price__gte": 5, "price__lte": 10},
    "5to20": {"price__gte": 5, "price__lte": 20},
    "5to50": {"price__gte": 5, "price__lte": 50},
    "10to20": {"price__gte": 10, "price__lte": 20},
    "10to50": {"price__gte": 10, "price__lte": 50},
    "20to50": {"price__gte": 20, "price__lte": 50},
    "50to100": {"price__gte": 50, "price__lte": 100},
}

CHANGE_PERCENTAGE_FILTER_MAPPING = {
    "u": {"change_percentage__gt": 0},
    "u1": {"change_percentage__gt": 1},
    "u2": {"change_percentage__gt": 2},
    "u3": {"change_percentage__gt": 3},
    "u4": {"change_percentage__gt": 4},
    "u5": {"change_percentage__gt": 5},
    "u6": {"change_percentage__gt": 6},
    "u7": {"change_percentage__gt": 7},
    "u8": {"change_percentage__gt": 8},
    "u9": {"change_percentage__gt": 9},
    "u10": {"change_percentage__gt": 10},
    "u15": {"change_percentage__gt": 15},
    "u20": {"change_percentage__gt": 20},
    "d": {"change_percentage__lt": 0},
    "d1": {"change_percentage__lt": -1},
    "d2": {"change_percentage__lt": -2},
    "d3": {"change_percentage__lt": -3},
    "d4": {"change_percentage__lt": -4},
    "d5": {"change_percentage__lt": -5},
    "d6": {"change_percentage__lt": -6},
    "d7": {"change_percentage__lt": -7},
    "d8": {"change_percentage__lt": -8},
    "d9": {"change_percentage__lt": -9},
    "d10": {"change_percentage__lt": -10},
    "d15": {"change_percentage__lt": -15},
    "d20": {"change_percentage__lt": -20},
}

AVG_VOLUME_FILTER_MAPPING = {
    "u50": {"avg_volume__lt": 50000},
    "u100": {"avg_volume__lt": 100000},
    "u500": {"avg_volume__lt": 500000},
    "u750": {"avg_volume__lt": 750000},
    "u1000": {"avg_volume__lt": 1000000},
    "o50": {"avg_volume__gt": 50000},
    "o100": {"avg_volume__gt": 100000},
    "o200": {"avg_volume__gt": 200000},
    "o300": {"avg_volume__gt": 300000},
    "o400": {"avg_volume__gt": 400000},
    "o500": {"avg_volume__gt": 500000},
    "o750": {"avg_volume__gt": 750000},
    "o1000": {"avg_volume__gt": 1000000},
    "o2000": {"avg_volume__gt": 2000000},
    "100to500": {"avg_volume__gte": 100000, "avg_volume__lte": 500000},
    "100to1000": {"avg_volume__gte": 100000, "avg_volume__lte": 1000000},
    "500to1000": {"avg_volume__gte": 500000, "avg_volume__lte": 1000000},
    "500to10000": {"avg_volume__gte": 500000, "avg_volume__lte": 10000000},
}

MARKET_CAP_FILTER_MAPPING = {
    "mega": {"market_cap__gte": 200_000_000_000},
    "large": {"market_cap__gte": 10_000_000_000, "market_cap__lt": 200_000_000_000},
    "mid": {"market_cap__gte": 2_000_000_000, "market_cap__lt": 10_000_000_000},
    "small": {"market_cap__gte": 300_000_000, "market_cap__lt": 2_000_000_000},
    "micro": {"market_cap__gte": 50_000_000, "market_cap__lt": 300_000_000},
    "nano": {"market_cap__lt": 50_000_000},
    "largeover": {"market_cap__gte": 10_000_000_000},
    "midover": {"market_cap__gte": 2_000_000_000},
    "smallover": {"market_cap__gte": 300_000_000},
    "microover": {"market_cap__gte": 50_000_000},
    "largeunder": {"market_cap__lt": 200_000_000_000},
    "midunder": {"market_cap__lt": 10_000_000_000},
    "smallunder": {"market_cap__lt": 2_000_000_000},
    "microunder": {"market_cap__lt": 300_000_000},
}

CCI_FILTER_MAPPING = {
    "ob100": {"indicators__cci__gt": 100},
    "ob50": {"indicators__cci__gt": 50},
    "os-100": {"indicators__cci__lt": -100},
    "os-50": {"indicators__cci__lt": -50},
    "positive": {"indicators__cci__gt": 0},
    "negative": {"indicators__cci__lt": 0},
    "50to100": {"indicators__cci__gte": 50, "indicators__cci__lte": 100},
    "neg100to50": {"indicators__cci__gte": -100, "indicators__cci__lte": -50},
    "neg50to0": {"indicators__cci__gte": -50, "indicators__cci__lt": 0},
    "0to50": {"indicators__cci__gte": 0, "indicators__cci__lte": 50},
}

RVI_FILTER_MAPPING = {
    "ob70": {"indicators__rv__gt": 70},
    "ob60": {"indicators__rv__gt": 60},
    "os30": {"indicators__rv__lt": 30},
    "os40": {"indicators__rv__lt": 40},
    "positive": {"indicators__rv__gt": 0},
    "negative": {"indicators__rv__lt": 0},
    "50to70": {"indicators__rv__gte": 50, "indicators__rv__lte": 70},
    "30to50": {"indicators__rv__gte": 30, "indicators__rv__lte": 50},
    "30to70": {"indicators__rv__gte": 30, "indicators__rv__lte": 70},
    "0to30": {"indicators__rv__gte": 0, "indicators__rv__lte": 30},
}

MFI_FILTER_MAPPING = {
    "ob80": {"indicators__mfi__gt": 80},
    "os20": {"indicators__mfi__lt": 20},
    "positive": {"indicators__mfi__gt": 50},
    "negative": {"indicators__mfi__lt": 50},
    "60to80": {"indicators__mfi__gte": 60, "indicators__mfi__lte": 80},
    "40to60": {"indicators__mfi__gte": 40, "indicators__mfi__lte": 60},
    "20to40": {"indicators__mfi__gte": 20, "indicators__mfi__lte": 40},
    "0to20": {"indicators__mfi__gte": 0, "indicators__mfi__lte": 20},
}

PERFORMANCE_INDEX_FILTER_MAPPING = {
    "high": {"indicators__performance_index__gt": 7},
    "medium": {
        "indicators__performance_index__gte": 7,
        "indicators__performance_index__lte": 3.5,
    },
    "low": {"indicators__performance_index__lt": 3.5},
}

AO_FILTER_MAPPING = {
    "positive": {"indicators__ao__gt": 0},
    "negative": {"indicators__ao__lt": 0},
    "above50": {"indicators__ao__gt": 50},
    "below50": {"indicators__ao__lt": 50},
    "high": {"indicators__ao__gt": 100},
    "low": {"indicators__ao__lt": -100},
    "50to100": {"indicators__ao__gte": 50, "indicators__ao__lte": 100},
    "0to50": {"indicators__ao__gte": 0, "indicators__ao__lte": 50},
    "neg50to0": {"indicators__ao__gte": -50, "indicators__ao__lt": 0},
    "neg100to50": {"indicators__ao__gte": -100, "indicators__ao__lte": -50},
}

SIGNAL_FILTER_MAPPING = {
    "0-20": {"indicators__signal__gte": 0, "indicators__signal__lt": 21},
    "21-40": {"indicators__signal__gte": 21, "indicators__signal__lt": 41},
    "41-60": {"indicators__signal__gte": 41, "indicators__signal__lt": 61},
    "61-80": {"indicators__signal__gte": 61, "indicators__signal__lt": 81},
    "81-100": {"indicators__signal__gte": 81, "indicators__signal__lte": 100},
}

SENTIMENT_FILTER_MAPPING = {
    "0-20": {"indicators__sentiment__gte": 0, "indicators__sentiment__lt": 21},
    "21-40": {"indicators__sentiment__gte": 21, "indicators__sentiment__lt": 41},
    "41-60": {"indicators__sentiment__gte": 41, "indicators__sentiment__lt": 61},
    "61-80": {"indicators__sentiment__gte": 61, "indicators__sentiment__lt": 81},
    "81-100": {"indicators__sentiment__gte": 81, "indicators__sentiment__lte": 100},
}

MA_ENV_SLOPE_FILTER_MAPPING = {
    "positive": {"indicators__ma_env_slope__gt": 0},
    "negative": {"indicators__ma_env_slope__lt": 0},
    "neg065to1": {
        "indicators__ma_env_slope__gte": -0.65,
        "indicators__ma_env_slope__lte": 1,
    },
    "1to2": {"indicators__ma_env_slope__gte": 1, "indicators__ma_env_slope__lte": 2},
    "2to5": {"indicators__ma_env_slope__gte": 2, "indicators__ma_env_slope__lte": 5},
    "above5": {"indicators__ma_env_slope__gt": 5},
}

MA_150_FILTER_MAPPING = {
    "above": {"price__gt": F("indicators__sma150")},
    "below": {"price__lt": F("indicators__sma150")},
}


STOCHASTIC_FILTER_MAPPING = {
    "crossover": {
        "indicators__stoch_k__gt": F("indicators__stoch_d"),
        "indicators__stoch_crossover": True,
    },
    "os20": {"indicators__stoch_k__lt": 20, "indicators__stoch_d__lt": 20},
    "ob80": {"indicators__stoch_k__gt": 80, "indicators__stoch_d__gt": 80},
    "k_above_d": {"indicators__stoch_k__gt": F("indicators__stoch_d")},
    "k_below_d": {"indicators__stoch_k__lt": F("indicators__stoch_d")},
}
