import json
import os
import re
from datetime import datetime, timedelta

import pytz
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.db.models import Case, ExpressionWrapper, F, FloatField, Q, When
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.views.decorators.csrf import csrf_exempt
from google.cloud import firestore
from google.oauth2 import service_account

from stocks.mapping_dict import (
    AO_FILTER_MAPPING,
    AVG_VOLUME_FILTER_MAPPING,
    CCI_FILTER_MAPPING,
    CHANGE_PERCENTAGE_FILTER_MAPPING,
    MA_150_FILTER_MAPPING,
    MA_ENV_SLOPE_FILTER_MAPPING,
    MARKET_CAP_FILTER_MAPPING,
    MFI_FILTER_MAPPING,
    PERFORMANCE_INDEX_FILTER_MAPPING,
    PRICE_FILTER_MAPPING,
    RVI_FILTER_MAPPING,
    SENTIMENT_FILTER_MAPPING,
    SIGNAL_FILTER_MAPPING,
    STOCHASTIC_FILTER_MAPPING,
)
from stocks.utils import base64_to_dict, convert_value

from .models import HistoricalData, SavedFilter, Stock

columns = [
    # {"key": "symbol", "label": "#"},
    {"key": "symbol", "label": "Symbol"},
    {"key": "name", "label": "Name"},
    {"key": "sector", "label": "Sector"},
    {"key": "price", "label": "Price"},
    {"key": "change_percentage", "label": "%Change"},
    {"key": "market_cap", "label": "Mkt Cap"},
    {"key": "indicators__cci", "label": "CCI"},
    {"key": "indicators__rv", "label": "RV"},
    {"key": "indicators__performance_index", "label": "PI"},
    {"key": "indicators__ao", "label": "AO"},
    {"key": "indicators__sentiment", "label": "Sentiment"},
    {"key": "indicators__signal", "label": "Signal"},
    {"key": "indicators__mfi", "label": "MFI"},
    {"key": "indicators__ma_env_slope", "label": "MA Slope"},
    {"key": "indicators__sma150", "label": "MA 150"},
    {"key": "indicators__stoch_k", "label": "Stoch Black"},
    {"key": "indicators__stoch_d", "label": "Stoch Red"},
    {"key": "avg_volume", "label": "Avg Vol"},
    {"key": "symbol", "label": "+/-"},
    {"key": "symbol", "label": "Add to Portfolio"},
    {"key": "symbol", "label": "Delete"},
]


@login_required
def stock_list(request):
    saved_name = request.GET.get("saved") or None
    if saved_name:
        saved_data = get_object_or_404(SavedFilter, name=saved_name)
        filter_url = saved_data.filter_url
        return redirect(filter_url)

    format_resp = request.GET.get("format") or None
    if format_resp == "json":
        # Define the timezone for US/Eastern
        eastern = pytz.timezone("America/New_York")
        # Get the current time in US/Eastern timezone
        now = datetime.now(eastern)
        # Define market open and close times
        market_open = datetime.strptime("09:30", "%H:%M").time()
        market_close = datetime.strptime("16:00", "%H:%M").time()
        # Check if today is a weekday (Monday to Friday)
        # and within market hours
        if now.weekday() >= 5 or not (market_open <= now.time() <= market_close):
            # Return an empty response if the market is closed
            return JsonResponse({})

    sort_by = request.GET.get("s") or "symbol"
    # Remove any leading hyphen for descending order
    clean_sort_by = re.sub(r"^-", "", sort_by)
    # Create a filter to exclude None values for the cleaned sorting column
    # exclude_filter = {f"{clean_sort_by}__isnull": True, clean_sort_by: 0}
    exclude_filter = Q(**{f"{clean_sort_by}__isnull": True}) | Q(**{clean_sort_by: 0})

    # Special handling for MA150 sorting
    if sort_by in ["indicators__sma150", "-indicators__sma150"]:
        stocks = Stock.objects.select_related("indicators").annotate(
            ma150_percentage=ExpressionWrapper(
                Case(
                    When(
                        Q(price__isnull=False)
                        & Q(indicators__sma150__isnull=False)
                        & ~Q(indicators__sma150=0),
                        then=(F("price") - F("indicators__sma150"))
                        * 100.0
                        / F("indicators__sma150"),
                    ),
                    default=None,
                ),
                output_field=FloatField(),
            )
        )

        # Change sort_by to use the calculated percentage
        sort_by = (
            "ma150_percentage"
            if sort_by == "indicators__sma150"
            else "-ma150_percentage"
        )
    else:
        # Remove any leading hyphen for descending order
        clean_sort_by = re.sub(r"^-", "", sort_by)
        # Create a filter to exclude None values for the cleaned sorting column
        exclude_filter = Q(**{f"{clean_sort_by}__isnull": True}) | Q(
            **{clean_sort_by: 0}
        )
        stocks = Stock.objects.select_related("indicators").exclude(exclude_filter)

    stocks = stocks.order_by(sort_by)

    sector = request.GET.get("sector") or None
    if sector:
        stocks = stocks.filter(sector=sector)

    query = request.GET.get("q") or None
    if query:
        stock_symbols = query.split(",")
        stock_symbols = [symbol.strip().upper() for symbol in stock_symbols]
        stocks = stocks.filter(symbol__in=stock_symbols)

    FILTER_KEYS = [
        "price",
        "change_percentage",
        "avg_volume",
        "market_cap",
        "cci",
        "rv",
        "mfi",
        "pi",
        "ao",
        "signal",
        "sentiment",
        "ma_env_slope",
        "ma150",
        "stochastic",
    ]

    FILTER_MAPPINGS = [
        PRICE_FILTER_MAPPING,
        CHANGE_PERCENTAGE_FILTER_MAPPING,
        AVG_VOLUME_FILTER_MAPPING,
        MARKET_CAP_FILTER_MAPPING,
        CCI_FILTER_MAPPING,
        RVI_FILTER_MAPPING,
        MFI_FILTER_MAPPING,
        PERFORMANCE_INDEX_FILTER_MAPPING,
        AO_FILTER_MAPPING,
        SIGNAL_FILTER_MAPPING,
        SENTIMENT_FILTER_MAPPING,
        MA_ENV_SLOPE_FILTER_MAPPING,
        MA_150_FILTER_MAPPING,
        STOCHASTIC_FILTER_MAPPING,
    ]

    # Iterate through the filter keys and their corresponding mappings
    for filter_key, filter_mapping in zip(FILTER_KEYS, FILTER_MAPPINGS):
        filter_value = request.GET.get(filter_key) or None
        if filter_value:
            filter_criteria = filter_mapping.get(filter_value) or None
            if filter_criteria:
                stocks = stocks.filter(**filter_criteria)
            else:
                # Handle custom range if provided
                custom_min = request.GET.get(f"{filter_key}_min") or None
                custom_max = request.GET.get(f"{filter_key}_max") or None
                if custom_min is not None and custom_max is not None:
                    custom_min = is_number(custom_min)
                    custom_max = is_number(custom_max)
                    if custom_min and custom_max:
                        if filter_key == "pi":
                            filter_key = "performance_index"
                        custom_filter = {
                            f"indicators__{filter_key}__gte": custom_min,
                            f"indicators__{filter_key}__lte": custom_max,
                        }
                        stocks = stocks.filter(**custom_filter)

    paginator = Paginator(stocks, 100)
    page_number = request.GET.get("page") or 1
    page_obj = paginator.get_page(page_number)

    if format_resp == "json":
        stocks_data = [
            {
                "symbol": stock.symbol,
                "name": stock.name,
                "sector": stock.sector,
                "price": stock.price,
                "change_percentage": stock.change_percentage,
                "market_cap": stock.market_cap,
                "cci": stock.indicators.cci,
                "rv": stock.indicators.rv,
                "avg_volume": stock.avg_volume,
                "mfi": stock.indicators.mfi,
                "ma_env_slope": stock.indicators.ma_env_slope,
                "ma150": (
                    (
                        (stock.price - stock.indicators.sma150)
                        / stock.indicators.sma150
                        * 100
                    )
                    if stock.price and stock.indicators.sma150
                    else None
                ),
                "performance_index": stock.indicators.performance_index,
                "ao": stock.indicators.ao,
                "signal": stock.indicators.signal,
                "sentiment": stock.indicators.sentiment,
                "stoch_k": stock.indicators.stoch_k,
                "stoch_d": stock.indicators.stoch_d,
            }
            for stock in page_obj
        ]
        return JsonResponse({"stocks": stocks_data})

    return render(
        request,
        "stocks/stock_list.html",
        {
            "page_obj": page_obj,
            "columns": columns,
        },
    )


def unique_sectors(request):
    unique_sectors = (
        Stock.objects.values_list("sector", flat=True).order_by("sector").distinct()
    )
    # Remove any None values
    unique_sectors = list(filter(None, unique_sectors))
    return JsonResponse({"sectors": unique_sectors})


def get_saved_filters(request):
    if request.method == "POST":
        id = request.POST.get("delete") or None
        if id:
            SavedFilter.objects.filter(id=id).delete()

        if request.POST.get("create"):
            name = request.POST.get("name") or None
            url = request.POST.get("url") or None
            SavedFilter.objects.update_or_create(
                name=name, defaults={"filter_url": url}
            )

    filters = SavedFilter.objects.all().order_by("-created_at")

    search = request.GET.get("q") or None
    if search:
        filters = filters.filter(name__icontains=search)

    format = request.GET.get("format") or None
    if format == "json":
        filters_data = [
            {
                "id": str(filter.id),
                "url": filter.filter_url,
                "name": filter.name,
            }
            for filter in filters
        ]
        return JsonResponse(filters_data, safe=False)

    filters_context = {"filters": filters}
    return render(request, "stocks/saved_filters.html", filters_context)


def is_number(value):
    try:
        return float(value)
    except ValueError:
        return None


@login_required
def historical_view(request):
    symbol = request.GET.get("symbol") or "AAPL"
    stock = get_object_or_404(Stock, symbol=symbol)
    historical_data = HistoricalData.objects.filter(stock=stock).order_by("-date")[:100]
    context = {"stock": stock, "historical_data": historical_data}
    return render(request, "stocks/historical_view.html", context)


@login_required
def market(request):
    # Your service account key as a dictionary
    service_account_base64_str = os.getenv("BASE64_STR") or None
    service_account_info = base64_to_dict(service_account_base64_str)

    # Initialize Firestore DB using the dictionary
    credentials = service_account.Credentials.from_service_account_info(
        service_account_info
    )
    db = firestore.Client(
        credentials=credentials,
        project=service_account_info["project_id"],
        database="raja-db",
    )

    # Fetch Fear/Greed Index and CBOE VIX Put/Call Ratio from Firestore
    doc_ref = db.collection("indices").document("fear_greed_and_vix")
    doc = doc_ref.get()

    if doc.exists:
        data = doc.to_dict()
        fear_greed_index = convert_value(data.get("fear_greed_score", 1000), int)
        total_put_call_ratio = convert_value(
            data.get("total_put_call_ratio", 1.0), float
        )
        equity_put_call_ratio = convert_value(
            data.get("equity_put_call_ratio", 1.0), float
        )
        was_above_75 = data.get("was_above_75", False)
    else:
        fear_greed_index = 1000
        total_put_call_ratio = 1.0
        equity_put_call_ratio = 1.0
        was_above_75 = False

    indices_symbols = ["^GSPC", "^RUT", "^VIX"]
    indices_obj = Stock.objects.filter(symbol__in=indices_symbols)
    sp500 = indices_obj[0]
    russell2000 = indices_obj[1]
    vix = indices_obj[2]

    def get_previous_ao_value(db):
        """
        Get the second most recent AO value from Firestore, skipping weekends
        Returns: The previous AO value or None if not found
        """
        # Get the last few documents, ordered by date descending
        docs = (
            db.collection("sp500_ao")
            .order_by("timestamp", direction=firestore.Query.DESCENDING)
            .limit(5)  # Get a few docs to handle weekends/holidays
            .stream()
        )

        # Convert to list and skip the most recent entry
        valid_docs = []
        for doc in docs:
            doc_date = datetime.strptime(doc.id, "%Y-%m-%d").date()
            # Skip weekends
            if doc_date.strftime("%A") not in ["Saturday", "Sunday"]:
                valid_docs.append(doc)

        # Get the second entry if available (skip most recent)
        if len(valid_docs) >= 2:
            ao_value = valid_docs[1].get("ao_value")
            # Handle if ao_value is a list
            if isinstance(ao_value, list):
                return ao_value[0]
            return ao_value

        return None

    # Get current and previous AO values
    current_ao = sp500.indicators.ao
    previous_ao = get_previous_ao_value(db)

    # Calculate required values
    price_below_150_sma = sp500.price < sp500.indicators.sma150 * 0.95
    price_below_16_sma = sp500.price < sp500.indicators.sma16 * 0.97
    price_within_3_below_16_sma = (sp500.price >= sp500.indicators.sma16 * 0.97) & (
        sp500.price <= sp500.indicators.sma16
    )
    price_within_5_below_150_sma = (sp500.price >= sp500.indicators.sma150 * 0.95) & (
        sp500.price <= sp500.indicators.sma150
    )

    current_ao = sp500.indicators.ao
    cci_or_ao_condition = sp500.indicators.cci < 100

    # Check if AO has dropped by 30 points or more compared to the previous day
    if previous_ao is not None:
        if isinstance(previous_ao, list):
            previous_ao = previous_ao[0]
        cci_or_ao_condition = cci_or_ao_condition or (previous_ao - current_ao >= 30)

    context = {
        "sp500": sp500,
        "russell2000": russell2000,
        "vix": vix,
        "equity_put_call_ratio": equity_put_call_ratio,
        "total_put_call_ratio": total_put_call_ratio,
        "fear_greed_index": fear_greed_index,
        "was_above_75": was_above_75,
        "price_below_150_sma": price_below_150_sma,
        "price_below_16_sma": price_below_16_sma,
        "price_within_3_below_16_sma": price_within_3_below_16_sma,
        "price_within_5_below_150_sma": price_within_5_below_150_sma,
        "cci_or_ao_condition": cci_or_ao_condition,
        "previous_ao": previous_ao,
        "current_ao": current_ao,
    }
    return render(request, "stocks/market.html", context)


@csrf_exempt
def modify_stock_in_list(request):
    if request.method == "POST":
        data = json.loads(request.body)
        stock_symbol = data.get("stock_symbol")
        list_id = data.get("list_id")
        action = data.get("action")

        saved_filter = get_object_or_404(SavedFilter, name=list_id.strip(","))
        filter_url = saved_filter.filter_url

        q_param = re.search(r"q=([^&]*)", filter_url)
        if q_param:
            q_values = q_param.group(1).split(",")
            if action == "add":
                if stock_symbol not in q_values:
                    q_values.append(stock_symbol)
            elif action == "remove":
                q_values = [val for val in q_values if val != stock_symbol]

            updated_q_param = ",".join(q_values)
            filter_url = re.sub(
                r"q=([^&]*)", f"q={updated_q_param.strip(',')}", filter_url
            )
        else:
            if action == "add":
                filter_url += f"&q={stock_symbol.strip(',')}"

        saved_filter.filter_url = filter_url.strip(",")
        saved_filter.save()

        return JsonResponse({"success": True, "filter_url": filter_url})

    return JsonResponse({"success": False})


@login_required
def delete_stock(request, symbol):
    if request.method == "POST":
        try:
            stock = Stock.objects.get(symbol=symbol)
            stock.delete()
            return redirect("stocks_list")
        except Stock.DoesNotExist:
            return redirect("stocks_list")
    return redirect("stocks_list")
