import gc
import logging
import os
import time
from concurrent.futures import ThreadPoolExecutor
from random import uniform

import pandas as pd
import yfinance as yf
from common.utils import (
    DBConnection,
    MockRequest,
    chunk_list,
    convert_to_float,
    execute_bulk_insert,
    get_stock_symbols,
    nan_to_none,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)


def fetch_single_stock(symbol):
    """Fetch data for a single stock symbol"""
    try:
        logging.info(f"Starting fetch for {symbol}")
        ticker = yf.Ticker(symbol)
        hist = ticker.history(period="1y")

        if hist.empty:
            logging.warning(f"No data found for {symbol}")
            return [], None

        # Process historical data
        historical_data = []
        hist = hist[::-1]
        hist = hist.where(pd.notna(hist), None)

        # Handle SP500 specific logic
        sp500_data = None
        if symbol == "^GSPC" and len(hist) >= 2:
            latest_close = hist["Close"].iloc[0]
            prev_close = hist["Close"].iloc[1]
            change_percentage = ((latest_close - prev_close) / prev_close) * 100
            sp500_data = (float(latest_close), float(change_percentage))

        # Convert historical data to list of tuples
        for date, row in hist.iterrows():
            try:
                historical_data.append(
                    (
                        symbol,
                        date.strftime("%Y-%m-%d"),
                        convert_to_float(row["Open"]),
                        convert_to_float(row["High"]),
                        convert_to_float(row["Low"]),
                        convert_to_float(row["Close"]),
                        nan_to_none(row["Volume"]),
                        "NOW()",
                        "NOW()",
                    )
                )
            except (KeyError, ValueError, TypeError) as e:
                logging.error(f"Error processing row for {symbol}: {str(e)}")
                continue

        # Clean up memory
        del hist
        gc.collect()

        return historical_data, sp500_data

    except Exception as e:
        logging.error(f"Error fetching {symbol}: {str(e)}")
        return [], None


def process_chunk(symbols_chunk):
    """Process a chunk of symbols with rate limiting and memory management"""
    all_historical_data = []
    sp500_data = None

    for symbol in symbols_chunk:
        try:
            time.sleep(uniform(0.3, 0.7))  # Rate limiting
            historical_data, current_sp500 = fetch_single_stock(symbol)

            if historical_data:
                all_historical_data.extend(historical_data)
            if current_sp500:
                sp500_data = current_sp500

            # Clean up after each symbol
            del historical_data
            gc.collect()

        except Exception as e:
            logging.error(f"Failed to process {symbol}: {str(e)}")
            continue

    return all_historical_data, sp500_data


def main(request):
    start_time = time.time()
    processed_records = 0

    try:
        # Parse request parameters
        request_json = request.get_json(silent=True) or {}
        part = int(request_json.get("part", 1))
        divided_by = int(request_json.get("divided_by", 2))

        # Single database connection for everything
        with DBConnection() as connection:
            with connection.cursor() as cursor:
                # Get symbols for this part yes
                stock_symbols = get_stock_symbols(cursor=cursor, only_symbols=True)
                total_symbols = len(stock_symbols)
                chunk_size = total_symbols // divided_by
                start_index = (part - 1) * chunk_size
                end_index = (
                    start_index + chunk_size if part != divided_by else total_symbols
                )
                selected_symbols = stock_symbols[start_index:end_index]

                logging.info(
                    f"Processing {len(selected_symbols)} symbols for part {part}/{divided_by}"
                )

                # Create chunks generator
                symbol_chunks = chunk_list(
                    selected_symbols, 5
                )  # Even smaller chunks for memory

                # Process chunks with ThreadPoolExecutor
                with ThreadPoolExecutor(max_workers=3) as executor:  # Reduced workers
                    futures = []

                    # Submit chunks to thread pool
                    for chunk in symbol_chunks:
                        futures.append(executor.submit(process_chunk, chunk))

                    # Process results as they complete and insert immediately
                    for future in futures:
                        try:
                            chunk_data, chunk_sp500 = future.result()

                            # Insert historical data for this chunk
                            if chunk_data:
                                insert_query = """
                                    INSERT INTO stocks_historicaldata
                                    (stock_id, date, open, high, low, close, volume, created_at, updated_at)
                                    VALUES %s
                                    ON CONFLICT (stock_id, date) DO UPDATE SET
                                        open = EXCLUDED.open,
                                        high = EXCLUDED.high,
                                        low = EXCLUDED.low,
                                        close = EXCLUDED.close,
                                        volume = EXCLUDED.volume,
                                        updated_at = NOW();
                                """
                                execute_bulk_insert(
                                    connection, cursor, insert_query, chunk_data
                                )
                                processed_records += len(chunk_data)

                                # Clean up chunk data after insert
                                del chunk_data
                                gc.collect()

                            # Update SP500 if available
                            if chunk_sp500:
                                cursor.execute(
                                    """
                                    UPDATE stocks_stock
                                    SET price = %s, change_percentage = %s, updated_at = NOW()
                                    WHERE symbol = '^GSPC';
                                    """,
                                    chunk_sp500,
                                )
                                connection.commit()

                        except Exception as e:
                            logging.error(f"Error processing chunk result: {str(e)}")
                            continue

                        # Clean up after each future
                        gc.collect()

        execution_time = time.time() - start_time
        logging.info(
            f"Completed in {execution_time:.2f}s. Processed {processed_records} records."
        )
        return f"Successfully updated {processed_records} historical records.", 200

    except Exception as e:
        logging.error(f"Critical error: {str(e)}")
        return f"Error processing data: {str(e)}", 500


if __name__ == "__main__" and os.getenv("SERVER") == "development":
    main(MockRequest())
