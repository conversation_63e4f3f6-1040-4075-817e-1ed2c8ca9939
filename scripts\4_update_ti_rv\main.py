import os
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
from datetime import datetime, timedelta
from multiprocessing import freeze_support

import pandas as pd
from common.utils import (
    DBConnection,
    calculate_relative_volatility,
    chunk_list,
    convert_to_float,
    execute_bulk_insert,
    get_stock_symbols,
)


def fetch_and_calculate_rv(data_chunk):
    """Calculate RV for the provided chunk of stock data"""
    try:
        rv_data = []
        temp_dict = {}

        # Group data by symbol
        for record in data_chunk:
            symbol = record[0]
            data = record[1:]

            if symbol not in temp_dict:
                temp_dict[symbol] = []

            temp_dict[symbol].append(data)

        # Calculate RV for each symbol
        for symbol, records in temp_dict.items():
            df = pd.DataFrame(records, columns=["date", "close", "high", "low"])
            try:
                df["RVI"] = calculate_relative_volatility(df)
            except Exception as e:
                print(str(e), "Error while calculate rv")
                continue

            latest_rv = (
                df["RVI"].dropna().iloc[-1] if not df["RVI"].dropna().empty else None
            )

            rv_data.append(
                (
                    symbol,
                    convert_to_float(latest_rv) if pd.notna(latest_rv) else None,
                    "NOW()",
                    "NOW()",
                )
            )

        return rv_data
    except Exception as e:
        print(f"Error in fetch_and_calculate_rv: {e}")
        return []


def main(request):
    try:
        start_time = datetime.now()

        # Single database connection for the entire process
        with DBConnection() as connection:
            with connection.cursor() as cursor:
                # Get stock symbols
                stock_symbols = get_stock_symbols(cursor=cursor, only_symbols=True)
                # stock_symbols = ["AAPL", "NEXT"]
                print(f"Retrieved {len(stock_symbols)} stock symbols.")

                # Create generator for symbol chunks
                chunks = chunk_list(stock_symbols, 25)

                format = "%Y-%m-%d"
                one_year_ago = (datetime.now() - timedelta(days=365)).strftime(format)

                # Process each chunk using ThreadPoolExecutor
                with ThreadPoolExecutor(max_workers=5) as executor:
                    futures = []

                    # Iterate over generator and submit tasks
                    for symbols_chunk in chunks:
                        # Fetch data for current chunk
                        symbol_placeholders = ",".join(["%s"] * len(symbols_chunk))
                        query = f"""
                            SELECT s.symbol, h.date, h.close, h.high, h.low
                            FROM stocks_stock s
                            JOIN stocks_historicaldata h ON s.symbol = h.stock_id
                            WHERE s.symbol IN ({symbol_placeholders}) AND h.date >= %s
                            ORDER BY s.symbol, h.date;
                        """.strip()
                        cursor.execute(query, symbols_chunk + [one_year_ago])
                        chunk_data = cursor.fetchall()

                        # Submit RV calculation to thread pool
                        futures.append(
                            executor.submit(fetch_and_calculate_rv, chunk_data)
                        )

                    # Collect results as they complete
                    rv_data = []
                    for future in futures:
                        chunk_result = future.result()
                        rv_data.extend(chunk_result)

                print(f"Calculated RV for {len(rv_data)} stocks")

                if rv_data:
                    insert_query = """
                        INSERT INTO stocks_indicators (stock_id, rv, updated_at, created_at)
                        VALUES %s
                        ON CONFLICT (stock_id) DO UPDATE
                        SET
                            rv = EXCLUDED.rv,
                            updated_at = NOW();
                    """.strip()

                    execute_bulk_insert(connection, cursor, insert_query, rv_data)
                    print("Bulk insert completed.")

        end_time = datetime.now()
        execution_time = end_time - start_time
        print(f"Execution time: {execution_time}")

        return f"RV values inserted for {len(rv_data)} stocks.", 200

    except Exception as e:
        print(f"Error in main function: {e}")
        raise


if __name__ == "__main__":
    if os.getenv("SERVER") == "development":
        freeze_support()
        main("")
