import base64
import json
import logging
import os
import random
import time
from datetime import datetime, timedelta
from io import BytesIO

import numpy as np
import pandas as pd
import psycopg2
import requests
import yfinance as yf
from dotenv import load_dotenv
from psycopg2 import OperationalError, extras

# Set up logging
load_dotenv()

logging.basicConfig(level=logging.INFO)


def fetch_api_data(url):
    """Fetch data from API."""
    try:
        logging.info(url)
        response = requests.get(url)
        if response.status_code == 200:
            return response.json()
    except Exception as e:
        logging.error(f"API Call Error: '{e}'")
    return None


class DBConnection:
    def __enter__(self):
        try:
            self.connection = psycopg2.connect(
                dbname=os.getenv("DB_NAME"),
                user=os.getenv("DB_USER"),
                password=os.getenv("DB_PWD"),
                host=os.getenv("DB_HOST"),
                port="5432",
            )
            return self.connection
        except OperationalError as e:
            print(e)
            logging.error(f"The error '{e}' occurred")
            return None

    def __exit__(self, exc_type, exc_value, traceback):
        if self.connection:
            self.connection.close()


def execute_select_query(cursor, query, values=None):
    """
    Execute a SELECT query using the given cursor, query, and optional values.
    Parameters:
        cursor (psycopg2.cursor): The database cursor.
        query (str): SQL query string.
        values (list, optional): Parameters to substitute into the query.
    Returns:
        list: The fetched rows as a list of tuples.
    """
    if isinstance(values, list):
        values = tuple(values)

    cursor.execute(query, values)
    return cursor.fetchall()


def execute_non_select_query(connection, cursor, query, values=None, batch=False):
    """
    Execute a non-SELECT SQL query on the database.
    Parameters:
        connection (psycopg2.connection): The database connection.
        cursor (psycopg2.cursor): The database cursor.
        query (str): SQL query string.
        values (list, optional): Parameters to substitute into the query.
        batch (bool): True if executemany should be used for batch processing.
    """
    if batch and isinstance(values, list):
        # For batch processing, use executemany
        cursor.executemany(query, values)
    else:
        # For individual execution, ensure values are in the correct format
        if isinstance(values, list):
            values = tuple(values)
        cursor.execute(query, values)
    connection.commit()


def get_stock_symbols(
    cursor=None,
    query="SELECT symbol FROM stocks_stock ORDER BY symbol;",
    only_symbols=False,
):
    """
    Fetch stock symbols from the database using the provided cursor and query.
    Parameters:
        cursor (psycopg2.cursor, optional): The database cursor. If None, a new connection is created.
        query (str): SQL query to fetch stock symbols.
        only_symbols (bool): If True, return only symbols; otherwise, return complete fetched data.
    Returns:
        list: A list of symbols or full query results.
    """
    if cursor is None:
        connection = DBConnection()
        cursor = connection.cursor()

    symbol_data = execute_select_query(cursor, query)

    if cursor is None:
        connection.close()
        cursor.close()

    if symbol_data is not None:
        if only_symbols:
            # Extracting the 'symbol' from each tuple in the list
            symbols = [item[0] for item in symbol_data]
            return symbols
        else:
            return symbol_data
    else:
        logging.error("No data found or there was an error fetching the data.")
        return []


def chunk_list(data_list, chunk_size):
    """Yield successive chunk_size-sized chunks from data_list."""
    for i in range(0, len(data_list), chunk_size):
        yield data_list[i : i + chunk_size]  # noqa


def execute_bulk_insert(connection, cursor, query, values, page_size=1000):
    """
    Execute a bulk insert operation using psycopg2's execute_values method.

    :param connection: The database connection object.
    :param cursor: The database cursor object.
    :param query: The SQL insert query string.
    :param values: A list of tuples containing the data to be inserted.
    :param page_size: The number of records to insert in each batch (default 1000).
    """
    # Execute the query using execute_values
    extras.execute_values(cursor, query, values, page_size=page_size)

    # Commit the changes
    connection.commit()


def execute_direct_query(connection, cursor, query):
    """
    Execute a direct SQL query on the database without expecting any return.
    Parameters:
        connection (psycopg2.connection): The database connection.
        cursor (psycopg2.cursor): The database cursor.
        query (str): SQL query string.
    """
    cursor.execute(query)
    connection.commit()


class MockRequest:
    def __init__(self, json_body=None, args=None):
        self._json = json_body or {"hello": "world"}
        self.args = args or {"hello": "world"}

    def get_json(self, silent=False):
        return self._json


def convert_to_float(value):
    """
    Convert a value to float if it is not None or a string.
    Returns:
        float: The converted float value if conversion is possible.
        None: If the input value is None or a string.
    """
    if value is not None and not pd.isna(value):
        try:
            return float(value)
        except (ValueError, TypeError):
            # Handle the case where the conversion is not possible
            return None
    return None


def calculate_cci(df, period=40):
    """
    Calculate the Commodity Channel Index (CCI).

    Parameters:
    df (pd.DataFrame): DataFrame containing 'High', 'Low', and 'Close' price columns.
    period (int): The period for the CCI calculation. Default is 40.

    Returns:
    pd.Series: A series containing the CCI values.
    """
    TP = (df["high"] + df["low"] + df["close"]) / 3
    SMA = TP.rolling(window=period).mean()
    mad = TP.rolling(window=period).apply(
        lambda x: np.mean(np.abs(x - np.mean(x))), raw=True
    )
    CCI = (TP - SMA) / (0.015 * mad)
    return CCI


def calculate_relative_volatility(df, length=10, ma_length=14):
    # Calculate standard deviation
    df["stddev"] = df["close"].rolling(window=length).std()

    # Calculate upper and lower
    df["change"] = df["close"].diff()
    df["upper"] = (
        df["stddev"].where(df["change"] > 0, 0).ewm(span=14, adjust=False).mean()
    )
    df["lower"] = (
        df["stddev"].where(df["change"] <= 0, 0).ewm(span=14, adjust=False).mean()
    )

    # Calculate RVI
    df["rvi"] = df["upper"] / (df["upper"] + df["lower"]) * 100

    # Calculate MA based on selected type
    df["rvi_ma"] = df["rvi"].rolling(window=ma_length).mean()

    return df["rvi"]


def calculate_mfi(df, period=20):
    """
    Calculate the Money Flow Index (MFI).

    Parameters:
    df (pd.DataFrame): DataFrame containing 'OHLCV' values.
    period (int): The period for the MFI calculation. Default is 20.

    Returns:
    pd.Series: A series containing the MFI values.
    """
    TP = (df["high"] + df["low"] + df["close"]) / 3
    MoneyFlow = TP * df["volume"]

    positive_flow = []
    negative_flow = []
    for i in range(1, len(TP)):
        if TP[i] > TP[i - 1]:
            positive_flow.append(MoneyFlow[i])
            negative_flow.append(0)
        elif TP[i] < TP[i - 1]:
            positive_flow.append(0)
            negative_flow.append(MoneyFlow[i])
        else:
            positive_flow.append(0)
            negative_flow.append(0)

    positive_mf = pd.Series(positive_flow).rolling(window=period).sum()
    negative_mf = pd.Series(negative_flow).rolling(window=period).sum()

    MFR = positive_mf / negative_mf
    MFI = 100 - (100 / (1 + MFR))
    return MFI


def calculate_performance_index(df, benchmark_df, period=120):
    """
    Calculate the Performance Index (PI) for the given DataFrame of stock data against a benchmark over a rolling period.

    Parameters:
    df (pd.DataFrame): DataFrame with stock data. It should contain 'Date' and 'Close' columns.
    benchmark_df (pd.DataFrame): DataFrame with benchmark data (e.g., S&P 500). It should contain 'Date' and 'Close' columns.
    period (int): The rolling period to use for calculations.

    Returns:
    pd.DataFrame: DataFrame with an additional 'PI' column representing the Performance Index.
    """
    # Ensure the 'Date' columns are datetime type
    df["date"] = pd.to_datetime(df["date"])
    benchmark_df["date"] = pd.to_datetime(benchmark_df["date"])

    # Merge stock data with benchmark data on 'date'
    merged_df = pd.merge(df, benchmark_df, on="date", suffixes=("", "_benchmark"))

    # Calculate the moving averages
    merged_df["Stock_MA"] = merged_df["close"].rolling(window=period).mean()
    merged_df["Benchmark_MA"] = (
        merged_df["close_benchmark"].rolling(window=period).mean()
    )

    # Calculate the Performance Index (PI)
    merged_df["PI"] = (
        merged_df["close"]
        / merged_df["close_benchmark"]
        * (merged_df["Benchmark_MA"] / merged_df["Stock_MA"])
    )

    return merged_df


def calculate_awesome_oscillator(df, short_window=5, long_window=34):
    """
    Calculate the Awesome Oscillator (AO).

    Parameters:
    df (pd.DataFrame): DataFrame containing 'High' and 'Low' price columns.
    short_window (int): The short period for the AO calculation. Default is 5.
    long_window (int): The long period for the AO calculation. Default is 34.

    Returns:
    pd.Series: A series containing the AO values.
    """
    median_price = (df["high"] + df["low"]) / 2
    short_sma = median_price.rolling(window=short_window).mean()
    long_sma = median_price.rolling(window=long_window).mean()
    AO = short_sma - long_sma
    return AO


def nan_to_none(value):
    """
    Convert NaN, None, or np.float64 values to None.
    Returns:
        None if the value is NaN, None, or np.float64, otherwise the original value.
    """
    if pd.isna(value) or value is None:
        return None
    if isinstance(value, np.float64):
        return float(value)
    return value


def fetch_csv_as_dataframe(url):
    """
    Fetches a CSV from the given URL and returns it as a pandas DataFrame.

    Parameters:
    url (str): The URL of the CSV file.

    Returns:
    pd.DataFrame: DataFrame containing the CSV data.
    """
    try:
        # Fetch the data from the URL
        response = requests.get(url)
        response.raise_for_status()  # Ensure we got a valid response

        # Load the data into a DataFrame
        df = pd.read_csv(BytesIO(response.content))

        return df
    except requests.exceptions.RequestException:
        return pd.DataFrame([])


# Load S&P 500 benchmark data from yfinance
def load_sp500_data_bak():
    try:
        sp500 = yf.Ticker("^GSPC")
        benchmark_df = sp500.history(period="1y")  # Get 1 year of data
        benchmark_df.reset_index(inplace=True)
        # Convert to string format
        benchmark_df["Date"] = benchmark_df["Date"].dt.strftime("%Y-%m-%d")
        benchmark_df.rename(columns={"Date": "date", "Close": "close"}, inplace=True)
        return benchmark_df
    except Exception as e:
        print("Getting SPY error", str(e))
        return pd.DataFrame([])


def load_sp500_data():
    try:
        api_key = os.getenv("FMP_API_KEY")
        if not api_key:
            raise ValueError("FMP_API_KEY not found in environment")

        from_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")
        url = f"https://financialmodelingprep.com/api/v3/historical-price-full/^GSPC?from={from_date}&apikey={api_key}"

        response = requests.get(url)
        if response.status_code != 200:
            raise Exception(f"Failed to fetch SP500 data: {response.status_code}")

        data = response.json().get("historical", [])
        if not data:
            return pd.DataFrame([])

        df = pd.DataFrame(data)
        df["date"] = pd.to_datetime(df["date"])
        df = df.sort_values("date").reset_index(drop=True)
        df["date"] = df["date"].dt.strftime("%Y-%m-%d")
        df.rename(columns={"close": "close"}, inplace=True)  # just to match naming

        return df[["date", "close"]]
    except Exception as e:
        print("Error loading SP500 benchmark from FMP:", str(e))
        return pd.DataFrame([])


def calculate_sma(df, period):
    df["sma"] = df["close"].rolling(window=period).mean()
    return df["sma"]


def get_random_user_agent():
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Safari/605.1.15",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (Linux; Android 11; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Mobile Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (Linux; Android 10; SM-A505F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Mobile Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
        "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.190 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.13; rv:68.0) Gecko/20100101 Firefox/68.0",
        "Mozilla/5.0 (Windows NT 6.1; WOW64; Trident/7.0; AS; rv:11.0) like Gecko",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.14; rv:78.0) Gecko/20100101 Firefox/78.0",
        "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.96 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:79.0) Gecko/20100101 Firefox/79.0",
        "Mozilla/5.0 (Windows NT 10.0; WOW64; rv:45.0) Gecko/20100101 Firefox/45.0",
        "Mozilla/5.0 (Linux; Android 9; SM-G960U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.105 Mobile Safari/537.36",
        "Mozilla/5.0 (Windows NT 6.1; WOW64; rv:60.0) Gecko/20100101 Firefox/60.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:85.0) Gecko/20100101 Firefox/85.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64; rv:86.0) Gecko/20100101 Firefox/86.0",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (Windows NT 6.1; WOW64; Trident/7.0; AS; rv:11.0) like Gecko",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.2 Safari/605.1.15",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.82 Safari/537.36",
        "Mozilla/5.0 (Linux; Android 11; Pixel 5 Build/RQ2A.210405.005) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.210 Mobile Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
        "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.1 Safari/605.1.15",
        "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_5) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.1 Safari/605.1.15",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
        "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
        "Mozilla/5.0 (Linux; Android 11; Pixel 4a Build/RQ2A.210405.005) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Mobile Safari/537.36",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:86.0) Gecko/20100101 Firefox/86.0",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.96 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.13; rv:78.0) Gecko/20100101 Firefox/78.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/90.0.818.56",
    ]
    return random.choice(user_agents)


def dict_to_base64(dictionary):
    json_str = json.dumps(dictionary)
    base64_bytes = base64.b64encode(json_str.encode("utf-8"))
    return base64_bytes.decode("utf-8")


def base64_to_dict(base64_str):
    base64_bytes = base64_str.encode("utf-8")
    json_str = base64.b64decode(base64_bytes).decode("utf-8")
    return json.loads(json_str)


def convert_numpy_bool(value):
    """Convert numpy boolean to Python boolean"""
    if pd.isna(value):
        return False
    if hasattr(value, "item"):  # Check if it's a numpy type
        return bool(value.item())
    return bool(value)


def calculate_ma_envelope_slope(df, period=50):
    """Calculate the slope of the MA envelope median line"""
    try:
        df["ma"] = df["close"].rolling(window=period).mean()
        if len(df["ma"].dropna()) > 5:
            recent_ma = df["ma"].dropna().tail(5)
            x = np.arange(len(recent_ma))
            y = recent_ma.values
            slope = np.polyfit(x, y, 1)[0]
            return (slope / recent_ma.mean()) * 100
        return None
    except Exception as e:
        logging.error(f"Error calculating MA envelope slope: {e}")
        return None


def calculate_stochastic(
    df,
    k_period=10,
    smoothing_period=3,
    double_smoothing_period=3,
    d_period=10,
):
    """Calculate Stochastic Momentum Index (SMI)"""
    try:
        highest_high = df["high"].rolling(window=k_period).max()
        lowest_low = df["low"].rolling(window=k_period).min()
        median_price = (highest_high + lowest_low) / 2
        relative_range = df["close"] - median_price
        highest_lowest_range = highest_high - lowest_low

        relative_range_smooth1 = relative_range.ewm(
            span=smoothing_period, adjust=False
        ).mean()
        relative_range_smooth2 = relative_range_smooth1.ewm(
            span=double_smoothing_period, adjust=False
        ).mean()
        range_smooth1 = highest_lowest_range.ewm(
            span=smoothing_period, adjust=False
        ).mean()
        range_smooth2 = range_smooth1.ewm(
            span=double_smoothing_period, adjust=False
        ).mean()

        df["smi"] = 200 * (relative_range_smooth2 / range_smooth2)
        df["smi_signal"] = df["smi"].ewm(span=d_period, adjust=False).mean()
        df["smi_crossover"] = (df["smi"] > df["smi_signal"]) & (
            df["smi"].shift(1) <= df["smi_signal"].shift(1)
        )

        return df
    except Exception as e:
        logging.error(f"Error calculating Stochastic: {e}")
        return df


class APIRateTracker:
    def __init__(self, limit=300, window=60):
        self.limit = limit
        self.window = window
        self.calls = []

    def add_call(self):
        now = time.time()
        self.calls.append(now)
        self.calls = [t for t in self.calls if now - t <= self.window]

    def can_make_call(self):
        now = time.time()
        self.calls = [t for t in self.calls if now - t <= self.window]
        return len(self.calls) < self.limit

    def wait_time(self):
        if self.can_make_call():
            return 0
        return self.window - (time.time() - self.calls[0])
