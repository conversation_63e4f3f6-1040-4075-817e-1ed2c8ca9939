import base64
import json


def convert_value(value, target_type):
    if value is None:
        return None
    try:
        if target_type == float:
            return float(value)
        elif target_type == int:
            return int(float(value))
        else:
            raise ValueError("target_type must be either float or int")
    except ValueError as e:
        print(f"Error converting value: {e}")
        return None


def base64_to_dict(base64_str):
    base64_bytes = base64_str.encode("utf-8")
    json_str = base64.b64decode(base64_bytes).decode("utf-8")
    return json.loads(json_str)
