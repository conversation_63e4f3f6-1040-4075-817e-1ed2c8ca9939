import os

import pandas as pd
from common.alert_utils import check_and_send_target_alerts
from common.utils import (
    DBConnection,
    convert_to_float,
    execute_bulk_insert,
    fetch_csv_as_dataframe,
    nan_to_none,
)


def update_portfolio_ranks(cursor):
    """Update portfolio ranks
    based on latest prices
    """
    cursor.execute(
        """
        WITH RankedStocks AS (
            SELECT
                pt.id,
                pt.stock_id,
                -- Safer calculation of potential gain
                CASE
                    WHEN s.price > 0 THEN ((pt.target_price - s.price) / s.price * 100)
                    ELSE 0
                END as potential_gain
            FROM portfolio_app_portfoliotarget pt
            JOIN stocks_stock s ON pt.stock_id = s.symbol
            -- Better filtering
            WHERE s.price IS NOT NULL AND s.price > 0
        )
        UPDATE portfolio_app_portfoliotarget pt
        SET
            previous_rank = pt.current_rank,  -- Save old rank
            current_rank = (                  -- Calculate new rank
                SELECT DENSE_RANK() OVER (ORDER BY rs.potential_gain DESC)
                FROM RankedStocks rs
                WHERE rs.id = pt.id
            )
        FROM RankedStocks rs
        WHERE pt.id = rs.id;
        """.strip()
    )


def main(request):
    url = "https://elite.finviz.com/export.ashx?auth=<EMAIL>&v=152&c=1,2,3,6,63,65,66"
    api_data = fetch_csv_as_dataframe(url)

    # Check if data is fetched
    if api_data.empty:
        return

    api_data["Change"] = api_data["Change"].str.rstrip("%").astype(float)
    api_data["Market Cap"] = api_data["Market Cap"].fillna(0).astype(float)
    api_data["Market Cap"] = (api_data["Market Cap"] * 1e6).astype("int64")
    api_data["Average Volume"] = api_data["Average Volume"].fillna(0).astype(float)
    api_data["Average Volume"] = (api_data["Average Volume"] * 1_000).astype("int64")
    api_data = api_data.where(pd.notnull(api_data), None)
    api_data = api_data.dropna(subset=["Ticker"])

    # Connect to database
    with DBConnection() as connection:
        with connection.cursor() as cursor:
            # SQL query to insert or update stock data
            query = """
            INSERT INTO stocks_stock (symbol, name, sector, price, change_percentage, market_cap, avg_volume, created_at, updated_at)
            VALUES %s
            ON CONFLICT (symbol) DO UPDATE SET
            name = EXCLUDED.name,
            sector = EXCLUDED.sector,
            price = EXCLUDED.price,
            change_percentage = EXCLUDED.change_percentage,
            market_cap = EXCLUDED.market_cap,
            avg_volume = EXCLUDED.avg_volume,
            updated_at = NOW();
            """

            # Prepare values for batch data
            values = [
                (
                    stock.get("Ticker"),
                    stock.get("Company"),
                    nan_to_none(stock.get("Sector")),
                    convert_to_float(stock.get("Price")),
                    convert_to_float(stock.get("Change")),
                    nan_to_none(stock.get("Market Cap")),
                    nan_to_none(stock.get("Average Volume")),
                    "NOW()",
                    "NOW()",
                )
                for _, stock in api_data.iterrows()
            ]

            execute_bulk_insert(connection, cursor, query, values)

            # After updating prices, update the portfolio ranks
            update_portfolio_ranks(cursor)

            # Check targets and send alerts
            check_and_send_target_alerts(connection)

    return f"{len(values)} Stocks updated successfully!"


if os.getenv("SERVER") == "development":
    print(main(""))
