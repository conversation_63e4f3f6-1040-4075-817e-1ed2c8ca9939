import os
from concurrent.futures import Thread<PERSON>oolExecutor
from datetime import datetime
from multiprocessing import freeze_support

import requests
from bs4 import BeautifulSoup
from common.utils import (
    DBConnection,
    chunk_list,
    convert_to_float,
    execute_bulk_insert,
    get_random_user_agent,
    get_stock_symbols,
)


def scrape_stock_data(symbol):
    url = f"https://www.stockscores.com/charts/charts/?ticker={symbol}"
    results = {"signal_stockscore": None, "sentiment_stockscore": None}
    try:
        headers = {"User-Agent": get_random_user_agent()}
        resp = requests.get(url, headers=headers)
        # Raise an error for bad status codes
        resp.raise_for_status()
        soup = BeautifulSoup(resp.text, "html.parser")

        for div in soup.find_all("div", class_="col-xs-6 center nobottommargin"):
            strong_element = div.find("strong")
            span_element = div.find("span", style="font-size:24px;")

            if strong_element and span_element:
                strong_text = (
                    strong_element.text.strip()
                    .replace(" ", "_")
                    .replace("\xa0", "_")
                    .lower()
                )
                score_element = span_element.find("b")

                if score_element:
                    score = score_element.text.strip()
                    results[strong_text] = convert_to_float(score)

        return results
    except requests.RequestException as e:
        print(f"Error fetching data for {symbol}: {e}")
        return results
    except Exception as e:
        print(f"General error scraping data for {symbol}: {e}")
        return results


def fetch_and_scrape_data(symbols_chunk):
    try:
        scraped_data = []

        for symbol in symbols_chunk:
            data = scrape_stock_data(symbol)
            scraped_data.append(
                (
                    symbol,
                    data["signal_stockscore"],
                    data["sentiment_stockscore"],
                    "NOW()",
                    "NOW()",
                )
            )

        return scraped_data
    except Exception as e:
        print(f"Error in fetch_and_scrape_data: {e}")
        return []


def main(request):
    try:
        start_time = datetime.now()

        with DBConnection() as connection:
            with connection.cursor() as cursor:
                stock_symbols = get_stock_symbols(
                    cursor=cursor,
                    only_symbols=True,
                )

        # stock_symbols = ["AAPL"]

        print(f"Retrieved {len(stock_symbols)} stock symbols.")

        # Use smaller chunks for better stability
        chunks = list(chunk_list(stock_symbols, 25))
        print(f"Chunked stock symbols into {len(chunks)} chunks.")

        # Replace Pool with ThreadPoolExecutor
        with ThreadPoolExecutor(max_workers=5) as executor:
            results = list(executor.map(fetch_and_scrape_data, chunks))
            scraped_data = [item for sublist in results for item in sublist]

        print("Finished scraping stock data.")
        print(f"Total scraped data to insert: {len(scraped_data)} records.")

        if scraped_data:
            insert_query = """
                INSERT INTO stocks_indicators (stock_id, signal, sentiment, updated_at, created_at)
                VALUES %s
                ON CONFLICT (stock_id) DO UPDATE
                SET
                    signal = EXCLUDED.signal,
                    sentiment = EXCLUDED.sentiment,
                    updated_at = NOW();
            """.strip()

            with DBConnection() as connection:
                with connection.cursor() as cursor:
                    execute_bulk_insert(connection, cursor, insert_query, scraped_data)
            print("Bulk insert completed.")

        end_time = datetime.now()
        execution_time = end_time - start_time
        print(f"Execution time: {execution_time}")

    except Exception as e:
        print(f"Error in main function: {e}")
        raise

    return f"Scraped data inserted for {len(stock_symbols)} stocks.", 200


# Main execution block to ensure this script runs standalone
if os.getenv("SERVER") == "development":
    if __name__ == "__main__":
        freeze_support()
        main("")
