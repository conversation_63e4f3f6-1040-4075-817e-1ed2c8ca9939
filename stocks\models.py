from django.db import models


class Stock(models.Model):
    symbol = models.CharField(max_length=10, primary_key=True, unique=True)
    name = models.CharField(max_length=500)
    price = models.FloatField(null=True, blank=True)
    change_percentage = models.FloatField(null=True, blank=True)
    avg_volume = models.BigIntegerField(null=True, blank=True)
    sector = models.CharField(max_length=500, null=True, blank=True)
    market_cap = models.BigIntegerField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class HistoricalData(models.Model):
    stock = models.ForeignKey(
        Stock, on_delete=models.CASCADE, related_name="historical_data"
    )
    date = models.DateField()
    open = models.FloatField(null=True, blank=True)
    high = models.FloatField(null=True, blank=True)
    low = models.FloatField(null=True, blank=True)
    close = models.FloatField(null=True, blank=True)
    volume = models.BigIntegerField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ("stock", "date")

    def __str__(self):
        return f"{self.stock.symbol} - {self.date}"


class Indicators(models.Model):
    stock = models.OneToOneField(
        Stock, on_delete=models.CASCADE, related_name="indicators"
    )
    cci = models.FloatField(null=True, blank=True)
    rv = models.FloatField(null=True, blank=True)
    mfi = models.FloatField(null=True, blank=True)
    performance_index = models.FloatField(null=True, blank=True)
    ao = models.FloatField(null=True, blank=True)
    cci_overbought = models.FloatField(null=True, blank=True)
    cci_oversold = models.FloatField(null=True, blank=True)
    rv_oversold = models.FloatField(null=True, blank=True)
    rv_overbought = models.FloatField(null=True, blank=True)
    mfi_overbought = models.FloatField(null=True, blank=True)
    mfi_oversold = models.FloatField(null=True, blank=True)
    signal = models.FloatField(null=True, blank=True)
    sentiment = models.FloatField(null=True, blank=True)
    sma8 = models.FloatField(null=True, blank=True)
    sma16 = models.FloatField(null=True, blank=True)
    sma150 = models.FloatField(null=True, blank=True)
    ma_env_slope = models.FloatField(null=True, blank=True)
    stoch_k = models.FloatField(null=True, blank=True)
    stoch_d = models.FloatField(null=True, blank=True)
    stoch_crossover = models.BooleanField(default=False, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Indicators for {self.stock.name}"


# from django.contrib.auth.models import User


class SavedFilter(models.Model):
    # user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='saved_filters')
    name = models.CharField(max_length=255)
    filter_url = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name
