name: Deploy Cloud Functions

on:
  push:
    branches: [main]
    paths:
      - 'scripts/**'
  workflow_dispatch:

jobs:
  deploy:
    name: Deploying Cloud Functions
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
        with:
          fetch-depth: 0

      - name: Set up Python 3.11
        uses: actions/setup-python@v2
        with:
          python-version: '3.11'

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v0
        with:
          project_id: ${{ secrets.PROJECT_ID }}
          service_account_key: ${{ secrets.SERVICE_ACCOUNT }}
          export_default_credentials: true

      - name: Deploy Changed Functions
        env:
          DB_HOST: ${{ secrets.DB_HOST }}
          DB_PWD: ${{ secrets.DB_PWD }}
          DB_USER: ${{ secrets.DB_USER }}
          DB_NAME: ${{ secrets.DB_NAME }}
          MAILJET_API_KEY: ${{ secrets.MAILJET_API_KEY }}
          MAILJET_API_SECRET: ${{ secrets.MAILJET_API_SECRET }}
          MAILJET_SENDER_EMAIL: ${{ secrets.MAILJET_SENDER_EMAIL }}
          BASE64_STR: ${{ secrets.BASE64_STR }}
          FMP_API_KEY: ${{ secrets.FMP_API_KEY }}
          GITHUB_BEFORE: ${{ github.event.before }}
          GITHUB_SHA: ${{ github.sha }}
          PROJECT_ID: ${{ secrets.PROJECT_ID }}
          SERVICE_ACCOUNT_EMAIL: ${{ secrets.SERVICE_ACCOUNT_EMAIL }}
        run: bash scripts/deploy_cloud_functions.sh